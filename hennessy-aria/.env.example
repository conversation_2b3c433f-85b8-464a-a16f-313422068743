# Hennessy ARIA Serverless Email Processing Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment stage (local, snd-hen, prd-hen)
STAGE=snd-hen

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=your-aws-account-id
AWS_PROFILE=snd-hen_qa

# =============================================================================
# SERVERLESS FRAMEWORK CONFIGURATION
# =============================================================================

# Serverless service name
SERVICE_NAME=hennesy

# Deployment bucket names per stage
DEPLOYMENT_BUCKET_LOCAL=local-hen-deployment-bucket
DEPLOYMENT_BUCKET_SND=snd-hen-deployment-bucket
DEPLOYMENT_BUCKET_PRD=prd-hen-deployment-bucket

# ECR Image URIs per stage
ECR_IMAGE_URI_SND=************.dkr.ecr.us-east-1.amazonaws.com/pyautomationaws/selenium:latest
ECR_IMAGE_URI_PRD=************.dkr.ecr.us-east-1.amazonaws.com/pyautomationaws/selenium:latest

# =============================================================================
# LAMBDA FUNCTION CONFIGURATION
# =============================================================================

# Lambda runtime and memory settings
LAMBDA_RUNTIME=python3.11
LAMBDA_MEMORY_SIZE=256
LAMBDA_TIMEOUT=900

# Lambda function environment variables (set automatically during deployment)
ENV=snd-hen
MONGO_DATABASE=snd_hennessy
BUCKET=snd-hen-bucket

# =============================================================================
# ARIA INTEGRATION
# =============================================================================

# ARIA Environment
ARIA_ENV=ariahennesy

# ARIA Application IDs per stage and document type
ARIA_APP_ID_POST_INVENTORY=234617d1-59bd-4b2f-b9b4-4ad5de283dd8
ARIA_APP_ID_BOLS=49b382f1-9de3-4372-9388-2e34b5d5c9f7
ARIA_APP_ID_TITLES=caa3d93e-9e96-4549-97a0-a241015b3dbd
ARIA_APP_ID_PRE_INVENTORY=
ARIA_APP_ID_USED_CARS=6a371f66-995c-43ce-a168-05849d04ff7e

# =============================================================================
# EMAIL PROCESSING CONFIGURATION
# =============================================================================

# Email folder configuration
SOURCE_FOLDER=inbox
PROCESSED_FOLDER=Processing
UNSORTED_FOLDER=04-Unsorted
ATTACHMENTS_FOLDER=attachments_files

# Email processing settings
TOP_FETCH=50
MOVE_EMAILS=false
FILTER_VALUE=

# Email notification settings
REPORT_EMAIL_USERS=<EMAIL>,<EMAIL>
SUPPORT_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>
ARIA_EMAIL=<EMAIL>
REPORTER_EMAIL=<EMAIL>
REPORTS_EMAIL=<EMAIL>
BCC_EMAIL=<EMAIL>,<EMAIL>

# =============================================================================
# LAMBDA FUNCTION REFERENCES
# =============================================================================

# Business Rules Engine
BRE_LAMBDA=snd-hen-bre
BRE_LAMBDA_PRE_INVENTORY=snd-hen-bre_pre_inventory
BRE_LAMBDA_USED_CARS=snd-hen-bre_used_cars
BRE_HANDLER_LAMBDA=snd-hen-bre_handler

# LLM Extraction Services
LLM_LAMBDA=snd-hen-llm_extractor
LLM_LAMBDA_PRE_INVENTORY=snd-hen-llm_extractor_pre_inventory
LLM_LAMBDA_USED_CARS=snd-hen-llm_extractor_used_cars
LLM_MESSENGER_LAMBDA=snd-hen-llm_messenger

# Document Processing
PDF_PROCESSER_LAMBDA=snd-hen-pdf_utils
PROCESS_EMAIL_LAMBDA=snd-hen-process_email
MOVE_EMAIL_LAMBDA=snd-hen-move_email

# Reporting and Integration
REPORT_TO_ARIA_LAMBDA=snd-hen-report_to_aria

# =============================================================================
# DOCUMENT STORAGE CONFIGURATION
# =============================================================================

# S3 folder structure
INVOICES_FOLDER=invoices
INVOICES_FOLDER_PRE_INVENTORY=invoices_new_cars
INVOICES_FOLDER_USED_CARS=invoices_used_cars
REYNOLS_REPORT_FOLDER=reynols_reports
PRE_INVENTORY_REPORT_FOLDER=
USED_CARS_REPORT_FOLDER=used_cars_reports

# =============================================================================
# SFTP CONFIGURATION
# =============================================================================

# SFTP settings (credentials stored in AWS Secrets Manager)
SFTP_CREDENTIALS=snd-hen-sftp_credentials
SFTP_FILES_PATH=/invokescans/reports
SFTP_FILES_PATH_PRE_INVENTORY=
SFTP_FILES_PATH_USED_CARS=/invokescans/reports
FILE_SFTP_EXTENSION=.csv

# =============================================================================
# WORKFLOW CONFIGURATION
# =============================================================================

# Processing stages
STAGE_POST_INVENTORY=post-inventory
STAGE_PRE_INVENTORY=pre-inventory
STAGE_USED_CARS=used-cars

# Group names for ARIA integration
GROUP_NAME=bol
GROUP_NAME_POST_INVENTORY=invoice
GROUP_NAME_PRE_INVENTORY=
GROUP_NAME_USED_CARS=used_cars_invoices
GROUP_NAME_BOLS=bol
GROUP_NAME_TITLES=titles

# =============================================================================
# LOCALSTACK CONFIGURATION (for local development)
# =============================================================================

# LocalStack settings
LOCALSTACK_HOST=http://localhost
LOCALSTACK_PORT=4566
LOCALSTACK_S3_ENDPOINT=http://localhost:4566

# Local S3 bucket for testing
LOCAL_S3_BUCKET=ach-deployment-bucket-local

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# CloudWatch configuration
LOG_LEVEL=INFO
DEBUG=false

# Error handling
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY=5

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API Gateway authorizer
LAMBDA_AUTHORIZER_TTL=300
LAMBDA_AUTHORIZER_PAYLOAD_VERSION=2.0

# Receiver key for external integrations
RECEIVER_KEY=snd-hen_invoke

# =============================================================================
# LAYER CONFIGURATION
# =============================================================================

# Lambda layers (automatically managed)
LAYER_BOTO3=Boto3Layer
LAYER_PYMONGO=PyMongoAWSLayer
LAYER_REQUESTS=RequestsLayer
LAYER_MSAL=MSALLayer
LAYER_PANDAS=PandasLayer
LAYER_OPENAI=OpenAILayer
LAYER_PYDANTIC=PydanticLayer
LAYER_PARAMIKO=ParamikoLayer
LAYER_VININFO=VinInfoLayer

# =============================================================================
# STEP FUNCTIONS CONFIGURATION
# =============================================================================

# Step Function state machines
STEP_FUNCTION_PROCESS_EMAILS=process_emails
STEP_FUNCTION_PROCESS_INVOICES=process_invoices
STEP_FUNCTION_PROCESS_TITLES=process_titles
STEP_FUNCTION_DOWNLOAD_INVOICES=download_and_process_invoice_flow

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development flags
ENABLE_TRACING=true
ENABLE_XRAY=false

# Performance settings
CONCURRENT_EXECUTIONS=10
RESERVED_CONCURRENCY=5

# =============================================================================
# AWS SECRETS MANAGER REFERENCES
# =============================================================================

# The following secrets should be configured in AWS Secrets Manager:
#
# Database:
# - {ENV}-mongodb_uri
#
# Email Credentials:
# - {ENV}-email_credentials
#   - mfa_outlook_config (main email account)
#   - {ENV}_hennessy_used_cars (used cars email account)
#
# SFTP Credentials:
# - {ENV}-sftp_credentials
#
# Store Credentials (inherited from core module):
# - {ENV}-user_login_for
# - {ENV}-user_login_loa
# - {ENV}-user_login_log
# - {ENV}-user_login_jlrn
# - {ENV}-user_login_por
# - {ENV}-user_login_cad
# - {ENV}-user_login_hon
# - {ENV}-user_login_mbg_mazda
#
# =============================================================================

# =============================================================================
# DEPLOYMENT COMMANDS REFERENCE
# =============================================================================

# Local deployment:
# npm run deploy:local

# Sandbox deployment:
# npm run deploy:snd

# Production deployment:
# npm run deploy:prd

# Deploy specific function:
# npm run deploy:snd:function --function=email_watcher

# Deploy specific Step Function:
# npm run deploy:snd:stepfunction --function=process_emails

# =============================================================================
