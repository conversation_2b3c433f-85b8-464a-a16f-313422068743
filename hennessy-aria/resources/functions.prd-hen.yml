functions:

  lambda_authorizer:
    name: prd-hen-lambda_authorizer
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LambdaAuthorizerRole
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/lambda_authorizer.zip
      patterns:
        - "!**/*"
        - src/lambda_authorizer/**

  bre_handler:
    name: prd-hen-bre_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      BRE_LAMBDA: ${self:provider.stage}-bre
      BR<PERSON>_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-bre_pre_inventory
      BRE_LAMBDA_USED_CARS: ${self:provider.stage}-bre_used_cars      
      LLM_LAMBDA: ${self:provider.stage}-llm_extractor
      LLM_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-llm_extractor_pre_inventory
      LLM_LAMBDA_USED_CARS: ${self:provider.stage}-llm_extractor_used_cars
      ARIA_APP_ID_POST_INVENTORY: 234617d1-59bd-4b2f-b9b4-4ad5de283dd8
      ARIA_APP_ID_BOLS: 49b382f1-9de3-4372-9388-2e34b5d5c9f7
      ARIA_APP_ID_TITLES: caa3d93e-9e96-4549-97a0-a241015b3dbd
      ARIA_APP_ID_PRE_INVENTORY: 
      ARIA_APP_ID_USED_CARS: 6a371f66-995c-43ce-a168-05849d04ff7e
    role: BreHandlerRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/bre_handler.zip
      patterns:
        - "!**/*"
        - src/bre_handler/**
    events:
      - httpApi:
          path: /${self:provider.stage}/bre_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  bre:
    name: prd-hen-bre
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      DEFAULT_BRE_TYPE: required_fields_and_calculations
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      ARIA_ENV: ariahennesy
    role: BreRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/bre.zip
      patterns:
        - "!**/*"
        - src/bre/**

  bre_used_cars:
    name: prd-hen-bre_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      DEFAULT_BRE_TYPE: required_fields_and_calculations
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      ARIA_ENV: ariahennesy
    role: BreUsedCarsRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/bre_used_cars.zip
      patterns:
        - "!**/*"
        - src/bre_used_cars/**

  llm_messenger:
    name: prd-hen-llm_messenger
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      SECRET_LLM_CREDENTIALS: ${self:provider.stage}-llm_params      
    role: LlmMessengerRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:13
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/llm_messenger.zip
      patterns:
        - "!**/*"
        - src/llm_messenger/**
  
  pdf_utils:
    name: prd-hen-pdf_utils
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
    role: PdfUtilsRole
    layers: 
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p312-PyMuPDF:3
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p312-boto3:14
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-reportlab:6
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/pdf_utils.zip
      patterns:
        - "!**/*"
        - src/pdf_utils/**

  email_watcher:
    name: prd-hen-email_watcher
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      DUPLICATED_FOLDER: Duplicates
      ENV: ${self:provider.stage}
      FILTER_VALUE: ''
      MONGO_DATABASE: prd_hennessy
      MOVE_EMAILS: false
      MOVE_EMAIL_LAMBDA: ${self:provider.stage}-move_email
      PROCESSED_FOLDER: Processing
      PROCESS_EMAIL_LAMBDA: ${self:provider.stage}-process_email
      REPORT_EMAIL_USERS: <EMAIL>,<EMAIL>
      SOURCE_FOLDER: inbox
      TOP_FETCH: 50
      UNSORTED_FOLDER: 04-Unsorted
    role: EmailWatcherRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/email_watcher.zip
      patterns:
        - "!**/*"
        - src/email_watcher/**

  move_email:
    name: prd-hen-move_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MOVE_EMAILS: true
    role: MoveEmailRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/move_email.zip
      patterns:
        - "!**/*"
        - src/move_email/**

  process_email:
    name: prd-hen-process_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ARIA_APP_ID: 49b382f1-9de3-4372-9388-2e34b5d5c9f7
      ARIA_ENV: ariahennesy
      ENV: ${self:provider.stage}
      BUCKET: ${self:provider.stage}-bucket
      FILTER_VALUE: ''
      GROUP_NAME: bol
      MONGO_DATABASE: prd_hennessy
      MOVE_EMAILS: false
      MOVE_EMAIL_LAMBDA: ${self:provider.stage}-move_email
      PROCESSED_FOLDER: Processing
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      PROCESS_EMAIL_LAMBDA: ${self:provider.stage}-process_email
      SOURCE_FOLDER: act_test_copy_dont_delete
      UNSORTED_FOLDER: 04-Unsorted
      ATTACHMENTS_FOLDER: attachments_files
    role: ProcessEmailRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyPDF2LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/process_email.zip
      patterns:
        - "!**/*"
        - src/process_email/**

  llm_extractor:
    name: prd-hen-llm_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.10
    architecture: x86_64
    memorySize: 2000
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BRE_LAMBDA: ${self:provider.stage}-bre
      ENV: ${self:provider.stage}
      LLM_EXTRACTOR_COLLECTION_NAME: llm_extractor
      MONGO_DATABASE: prd_hennessy
      aria_env: ariahennesy
      RETRYS: 3
    role: LLMExtractorRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}      
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/llm_extractor.zip
      patterns:
        - "!**/*"
        - src/llm_extractor/**

  llm_extractor_used_cars:
    name: prd-hen-llm_extractor_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.10
    architecture: x86_64
    memorySize: 2000
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BRE_LAMBDA_USED_CARS: ${self:provider.stage}-bre_used_cars
      ENV: ${self:provider.stage}
      LLM_EXTRACTOR_COLLECTION_NAME: llm_extractor
      MONGO_DATABASE: prd_hennessy
      aria_env: ariahennesy
      RETRYS: 3
    role: LLMExtractorUsedCarsRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}      
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/llm_extractor_used_cars.zip
      patterns:
        - "!**/*"
        - src/llm_extractor_used_cars/**

  report_sftp_to_s3:
    name: prd-hen-report_sftp_to_s3
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      REYNOLS_REPORT_FOLDER: reynols_reports
      SFTP_CREDENTIALS: ${self:provider.stage}-sftp_credentials
      SFTP_FILES_PATH: /invokescans/reports
      FILE_SFTP_EXTENSION: .csv
      PRE_INVENTORY_REPORT_FOLDER:
      SFTP_FILES_PATH_PRE_INVENTORY:
      USED_CARS_REPORT_FOLDER:
      SFTP_FILES_PATH_USED_CARS:
    role: ReportSFTPToS3Role
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-paramiko:13
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/reynols_report_processor.zip
      patterns:
        - "!**/*"
        - src/reynols_report_processor/**

  invoice_downloader:
    name: prd-hen-invoice_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      INVOICES_FOLDER: invoices
      INVOICES_FOLDER_PRE_INVENTORY: 
      INVOICES_FOLDER_USED_CARS: 
    role: InvoiceDownloaderRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/invoice_downloader.zip
      patterns:
        - "!**/*"
        - src/invoice_downloader/**

  invoice_to_aria:
    name: prd-hen-invoice_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: ariahennesy
      GROUP_NAME_POST_INVENTORY: invoice
      ARIA_APP_ID_POST_INVENTORY: 234617d1-59bd-4b2f-b9b4-4ad5de283dd8
      GROUP_NAME_PRE_INVENTORY: 
      ARIA_APP_ID_PRE_INVENTORY: 
      GROUP_NAME_USED_CARS: used_cars_invoices
      ARIA_APP_ID_USED_CARS: 6a371f66-995c-43ce-a168-05849d04ff7e
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
    role: InvoiceToAriaRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/invoice_to_aria.zip
      patterns:
        - "!**/*"
        - src/invoice_to_aria/**

  title_sftp_to_s3_to_aria:
    name: prd-hen-title_sftp_to_s3_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: ariahennesy
      GROUP_NAME: title
      ARIA_APP_ID: caa3d93e-9e96-4549-97a0-a241015b3dbd
      COMPLETE_TITLE_FOLDER: titles
      ENV: ${self:provider.stage}
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      SFTP_FILES_PATH: /invokescans
      FILE_SFTP_EXTENSION: .pdf
      MONGO_DATABASE: prd_hennessy
      SFTP_CREDENTIALS: ${self:provider.stage}-sftp_credentials
    role: TitleSFTPToS3ToAriaRole
    layers: 
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-paramiko:13
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-PyMuPDF:8
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/title_sftp_to_s3_to_aria.zip
      patterns:
        - "!**/*"
        - src/title_sftp_to_s3_to_aria/**
        
  orchestrator_downloader:
    name: prd-hen-orchestrator_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: ariahennesy
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
    role: OrchestratorDownloaderRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/orchestrator_downloader.zip
      patterns:
        - "!**/*"
        - src/orchestrator_downloader/**

  reconciliate:
    name: prd-hen-reconciliate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      ARIA_ENV: ariahennesy
      BRE_HANDLER_LAMBDA: ${self:provider.stage}-bre_handler
      BUCKET: ${self:provider.stage}-bucket
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      REPORT_TO_ARIA_LAMBDA: ${self:provider.stage}-report_to_aria
    role: ReconciliateRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/reconciliate.zip
      patterns:
        - "!**/*"
        - src/reconciliate/**

  orchestrator_download_update:
    name: prd-hen-orchestrator_download_update
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: ariahennesy
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      REPORT_EMAIL: <EMAIL>,<EMAIL>,<EMAIL>
      SUPPORT_EMAIL: <EMAIL>,<EMAIL>,<EMAIL>
      ARIA_EMAIL: <EMAIL>
      REPORT_TO_ARIA_LAMBDA: ${self:provider.stage}-report_to_aria
    role: OrchestratorDownloadUpdateRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/orchestrator_download_update.zip
      patterns:
        - "!**/*"
        - src/orchestrator_download_update/**

  selenium_downloader:
    name: prd-hen-selenium_downloader
    image: 
      name: selenium-downloader-latest
    memorySize: 1024
    timeout: 900
    architecture: x86_64
    role: SeleniumDownloaderRole
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    environment:
      ENV: ${self:provider.stage}
      DB_NAME: prd_hennessy

  reevaluate:
    name: prd-hen-reevaluate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      ARIA_ENV: ariahennesy
      BRE_HANDLER_LAMBDA: ${self:provider.stage}-bre_handler
      BUCKET: ${self:provider.stage}-bucket
    role: ReevaluateRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/reevaluate.zip
      patterns:
        - "!**/*"
        - src/reevaluate/**

  python_handler:
    name: prd-hen-python_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: PythonHandlerRole
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/python_handler.zip
      patterns:
        - "!**/*"
        - external_modules/orchestrator_utilities/processes/rpa_processes_handler/**
    events:
      - httpApi:
          path: /${self:provider.stage}/python_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  loading_wi_report:
    name: prd-hen-loading_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: ariahennesy
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL> #<EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
    role: LoadingWiReportRole
    layers:
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/loading_wi_report.zip
      patterns:
        - "!**/*"
        - src/loading_wi_report/**

  error_wi_report:
    name: prd-hen-error_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: ariahennesy
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL> #<EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: prd_hennessy  
      RECEIVER_KEY: ${self:provider.stage}_invoke # hennesy
    role: ErrorWiReportRole
    layers:
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/error_wi_report.zip
      patterns:
        - "!**/*"
        - src/error_wi_report/**

  report_loaded_data:
    name: prd-hen-report_loaded_data
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: ariahennesy
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: prd_hennessy  
    role: ReportLoadedDataRole
    layers:
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-jinja2:6
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/report_loaded_data.zip
      patterns:
        - "!**/*"
        - src/report_loaded_data/**

  queues_handler:
    name: prd-hen-queues_handler
    handler: lambda_function.lambda_handler
    events:
      - httpApi:
          path: /${self:provider.stage}/queues_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: QueuesHandlerRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/queues_handler.zip
      patterns:
        - "!**/*"
        - src/queues_handler/**

  report_pages_not_used_in_titles:
    name: prd-hen-report_pages_not_used_in_titles
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: ariahennesy
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: prd_hennessy  
    role: ReportPagesNotUsedInTitlesRole
    layers:
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-jinja2:6
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/report_pages_not_used_in_titles.zip
      patterns:
        - "!**/*"
        - src/report_pages_not_used_in_titles/**
        
  secrets_handler:
    name: prd-hen-secrets_handler
    handler: lambda_function.lambda_handler
    events:
      - httpApi:
          path: /${self:provider.stage}/secrets_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: SecretsHandlerRole
    layers:
      - {Ref: Boto3LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/secrets_handler.zip
      patterns:
        - "!**/*"
        - src/secrets_handler/**

  report_to_aria:
    name: prd-hen-report_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: ariahennesy
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      GROUP_NAME: reports
      ARIA_APP_ID: 10526188-7fdb-4c07-a9a9-6db3bf9bd2e7
      REYNOLS_REPORT_FOLDER: reynols_reports
    role: ReportToAriaRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - {Ref: OpenpyxlLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/report_to_aria.zip
      patterns:
        - "!**/*"
        - src/report_to_aria/**
    events:
      - httpApi:
          path: /${self:provider.stage}/report_to_aria
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  completed_vins_validation:
    name: prd-hen-completed_vins_validation
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      FILE_SFTP_EXTENSION: .csv
      ARIA_APP_ID: 234617d1-59bd-4b2f-b9b4-4ad5de283dd8
      REYNOLS_REPORT_FOLDER: reynols_reports
      ARIA_ENV: ariahennesy
    role: CompletedVinsValidationRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/completed_vins_validation.zip
      patterns:
        - "!**/*"
        - src/completed_vins_validation/**

  load_pricing_guide:
    name: prd-hen-load_pricing_guide
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      REYNOLS_REPORT_FOLDER: reynols_reports
      ARIA_ENV: ariahennesy
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      PRICING_GUIDE_FOLDER: invoices_information
    role: PricingGuideRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p312-PyMuPDF:3
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/load_pricing_guide.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide/**

  load_pricing_guide_extractor:
    name: prd-hen-load_pricing_guide_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: prd_hennessy
      ARIA_ENV: ariahennesy
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      BUCKET_FOLDER: textract_operations
    role: PricingGuideExtractorRole
    layers: 
      - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0e34958ac5ac835e6
        - sg-0e74b20a104887796
      subnetIds:
        - subnet-097891c443a14b42d
        - subnet-0d8de56bd8f7144fc
    package:
      individually: true
      artifact: artifacts/load_pricing_guide_extractor.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide_extractor/**