{"StartAt": "InvoiceDownloader", "States": {"InvoiceDownloader": {"Type": "Task", "Parameters": {"stage.$": "$.stage"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-invoice_downloader", "ResultPath": "$.result", "Next": "ProcessInvoiceMap"}, "ProcessInvoiceMap": {"Type": "Map", "ItemsPath": "$.result.body.vins_to_be_processed", "Parameters": {"stage.$": "$.stage", "vin.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "ProcessingInvoice", "States": {"ProcessingInvoice": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-invoice_to_aria", "End": true, "ResultPath": null}}}, "Next": "ReportLoadedData", "ResultPath": null}, "ReportLoadedData": {"Type": "Task", "Parameters": {"stage.$": "$.stage", "collection": "invoice"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-report_loaded_data", "Next": "WaitToProcessAllItems", "ResultPath": null}, "WaitToProcessAllItems": {"Type": "Wait", "Seconds": 600, "Next": "LoadingStuckWiReport"}, "LoadingStuckWiReport": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-loading_wi_report", "Parameters": {"app": "invoice", "stage.$": "$.stage"}, "End": true}}}