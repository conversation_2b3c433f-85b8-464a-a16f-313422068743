{"StartAt": "EmailWatcherH", "States": {"EmailWatcherH": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-email_watcher", "ResultPath": "$.result", "Next": "MapProcessEmail"}, "MapProcessEmail": {"Type": "Map", "ItemsPath": "$.result.body.emails_to_be_processed", "ResultPath": null, "Parameters": {"email_id.$": "$$.Map.Item.Value", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "ProcessingEmail", "States": {"ProcessingEmail": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-process_email", "ResultPath": null, "End": true}}}, "Next": "DetermineAppType"}, "DetermineAppType": {"Type": "Choice", "Choices": [{"Variable": "$.result.stage", "StringEquals": "post-inventory", "Next": "SetAppToBol"}], "Default": "SetAppToInvoice"}, "SetAppToBol": {"Type": "Pass", "Result": {"app": "bol"}, "ResultPath": "$.app", "Next": "LoadingStuckWiReport"}, "SetAppToInvoice": {"Type": "Pass", "Result": {"app": "invoice"}, "ResultPath": "$.app", "Next": "LoadingStuckWiReport"}, "LoadingStuckWiReport": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-loading_wi_report", "Parameters": {"app.$": "$.app.app", "stage.$": "$.result.stage"}, "End": true}}}