{"StartAt": "DownloadPricingGuide", "States": {"DownloadPricingGuide": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.downloadResult", "Next": "CheckDownloadStatus"}, "CheckDownloadStatus": {"Type": "Choice", "Choices": [{"Variable": "$.downloadResult.statusCode", "NumericEquals": 200, "Next": "ProcessPriceGuide"}], "Default": "EndState"}, "ProcessPriceGuide": {"Type": "Task", "Parameters": {"store.$": "$.store"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-load_pricing_guide", "ResultPath": "$.result", "Next": "ProcessPagesPricingGuide"}, "ProcessPagesPricingGuide": {"Type": "Map", "ItemsPath": "$.result.pages_to_process", "Parameters": {"store.$": "$.store", "items.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "ProcessPagePricingGuide", "States": {"ProcessPagePricingGuide": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-load_pricing_guide_extractor", "End": true}}}, "End": true}, "EndState": {"Type": "Succeed"}}}