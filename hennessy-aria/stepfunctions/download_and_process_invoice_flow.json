{"Comment": "Complete flow from reading an email from the invoice to creating it in ARIA", "StartAt": "DownloadInvoiceStep", "States": {"DownloadInvoiceStep": {"Type": "Task", "Resource": "arn:aws:states:::states:startExecution.sync", "Parameters": {"StateMachineArn": "arn:aws:states:##REGION##:##ACCOUNT_ID##:stateMachine:##ENV##-invoice-downloader-orchestrator", "Input": {"stage.$": "$.stage"}}, "Next": "WaitToProcessAllItems", "ResultPath": null}, "WaitToProcessAllItems": {"Type": "Wait", "Seconds": 300, "Next": "ProcessInvoiceStep"}, "ProcessInvoiceStep": {"Type": "Task", "Resource": "arn:aws:states:::states:startExecution.sync", "Parameters": {"StateMachineArn": "arn:aws:states:##REGION##:##ACCOUNT_ID##:stateMachine:##ENV##-process-invoices", "Input": {"stage.$": "$.stage"}}, "End": true}}}