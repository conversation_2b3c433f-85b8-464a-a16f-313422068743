{"StartAt": "OrchestratorDownloader", "States": {"OrchestratorDownloader": {"Type": "Task", "Parameters": {"stage.$": "$.stage"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_downloader", "ResultPath": "$.result", "Next": "StoreMap"}, "StoreMap": {"Type": "Map", "ItemsPath": "$.result.body", "Parameters": {"store.$": "$$.Map.Item.Value.store", "batches.$": "$$.Map.Item.Value.batches", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "VinBatchMap", "States": {"VinBatchMap": {"Type": "Map", "ItemsPath": "$.batches", "MaxConcurrency": 1, "Parameters": {"stage.$": "$.stage", "store.$": "$.store", "data.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "SeleniumDownloader", "States": {"SeleniumDownloader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.result", "End": true}}}, "End": true}}}, "Next": "OrchestratorDownloadUpdate"}, "OrchestratorDownloadUpdate": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_download_update", "End": true}}}