import bre_files.rules as r
from utils.mongo_utils            import Mongo
from utils.crud_handler_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>
from bre_files.bre_rules import get_rules_json, VALID_RULES
from utils.crud_bre_results import CrudBreResults
import time
from datetime import datetime
import os
import json
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class Bre():

    def __init__(self, event, mongo_uri):

        # Init mongo
        self.mongo_client = Mongo(mongo_uri)
        self.mongo_uri = mongo_uri

        self.now_in_ms = datetime.now()

        self.event = event
        self.bre_type = self.event.get('bre_type', '')
        self.document = self.event.get('document', {})
        self.action = self.event.get('action', {})
        self.execution_id = self.event.get('execution_id', '')
        self.request_response = self.event.get('request_response', False)
        self.rules_output_log = {}
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_bre_results = CrudBreResults(self.mongo_client)
        # Extracting active rules

        # Parsing input json
        self.document_id = self.document['id']
        self.app_id = self.document.get('app_id')
        
        if not self.app_id:
            raise Exception("app_id is missing from the document")
                    
        self.ocr_groups = self.document.get('ocr_groups', [])
        if not self.ocr_groups:
            raise Exception('No groups found in document')
        
        self.valid_rules = []
        
        group = self.ocr_groups[0]
        self.document_type = group

        if self.bre_type == '':
            if "invoice" in self.ocr_groups[0]:
                self.bre_type = {'invoice': os.environ['DEFAULT_BRE_TYPE']}
            if "bol" in self.ocr_groups[0]:
                self.bre_type = {'bol': os.environ['DEFAULT_BRE_TYPE']}
            if "title" in self.ocr_groups[0]:
                self.bre_type = {'title': os.environ['DEFAULT_BRE_TYPE']}


        if "invoice" in group:
            self.brand = self.document['groups'][self.document_type]['fields']['make']['value'].lower()
            if self.brand in VALID_RULES[self.document_type].keys():
                self.valid_rules.extend(VALID_RULES[self.document_type][self.brand].get(self.bre_type[self.document_type], []))
        else:
            #print("**********************", self.bre_type)
            self.valid_rules = VALID_RULES[self.document_type].get(self.bre_type[self.document_type], [])
        

        print("RULES", self.valid_rules)
        print(self.bre_type)
            

        #self.valid_rules = list(set(self.valid_rules))
        #self.valid_rules.sort()
        
        self.extracted_fields = {}
        for group in self.ocr_groups:
            group_fields = self.document['groups'].get(group, {}).get('fields', {})
            self.extracted_fields[group] = group_fields

        self.parsed_fields = {}

    def call_rule(self, rule_nr, rule_json, passed_rules, results_json):
        process = True
        pre_req = rule_json['pre_req']
        arg = rule_json['arguments']
        needed_results_params = rule_json.get('rules_results_values', None)

        if needed_results_params is not None:
            for param in needed_results_params:
                arg.append(results_json[param]['value'])
        
        self.rules_output_log[f"rule_{rule_nr}"] = {}
        self.rules_output_log[f"rule_{rule_nr}"]['input'] = arg

        for item in pre_req:
            if item > 0:
                if item not in passed_rules:
                    process = False
            else:
                if abs(item) in passed_rules:
                    process = False
        if process:
            rule_method = getattr(r, f"rule_{rule_nr}")
            result = rule_method(*arg)
            self.rules_output_log[f"rule_{rule_nr}"]['output'] = result
            return result
        else:
            return None
        
    def execute_rules(self, rules_json, rules_with_pre_req):
        results_json = {}
        passed_rules = []
        not_passed_rules = []

        rules_without_pre_req = [rule for rule in self.valid_rules if rule not in rules_with_pre_req]

        def execute_rule(item):
            """Helper function to execute a rule and return results."""
            result = self.call_rule(item, rules_json[item], passed_rules, results_json)
            return item, result

        # **1. Execute rules WITHOUT prerequisites in parallel**
        with ThreadPoolExecutor(max_workers=8) as executor:
            future_to_rule = {executor.submit(execute_rule, item): item for item in rules_without_pre_req}
            
            for future in as_completed(future_to_rule):
                item, result = future.result()
                if result:
                    if result[rules_json[item]['tag']]['pass']:
                        passed_rules.append(item)
                        for tag in rules_json[item]['output_tags']:
                            results_json[tag] = result[tag]
                    else:
                        not_passed_rules.append(item)
                        for tag in rules_json[item]['output_tags']:
                            if result.get(tag, None):
                                results_json[tag] = result[tag]
                    
                    if result.get('aria_exception', None):
                        result.pop('aria_exception')

        # **2. Execute rules WITH prerequisites sequentially**
        for item in rules_with_pre_req:
            result = self.call_rule(item, rules_json[item], passed_rules, results_json)
            
            if result:
                if result[rules_json[item]['tag']]['pass']:
                    passed_rules.append(item)
                    for tag in rules_json[item]['output_tags']:
                        results_json[tag] = result[tag]
                else:
                    not_passed_rules.append(item)
                    for tag in rules_json[item]['output_tags']:
                        if result.get(tag, None):
                            results_json[tag] = result[tag]
                
                if result.get('aria_exception', None):
                    result.pop('aria_exception')

        return results_json, passed_rules, not_passed_rules
    
    def get_parsed_fields(self):
        parsed_fields = {}
        # Process fields from each group
        for group_name, group_fields in self.extracted_fields.items():
            parsed_fields[group_name] = {}
            for k, v in group_fields.items():
                if isinstance(v.get('value'), str):
                    parsed_fields[group_name][k] = {
                        'display': v.get('display', True),
                        'pass': True,
                        'message': '',
                        'value': v.get('value', v.get('rows', '')),
                        'coordinates': v.get('coordinates', {})
                    }
                else:

                    parsed_fields[group_name][k] = v
                    if not parsed_fields[group_name][k].get('pass', False):
                        parsed_fields[group_name][k]['pass'] = True
                        parsed_fields[group_name][k]['message'] = ''

                    if "rows" in v:
                        for kk, vv in parsed_fields[group_name][k].get('rows', {}).items():
                            for kkk, vvv in vv['cells'].items():
                                vvv['pass'] = True
                                vvv['message'] = ''

                    else:
                        parsed_fields[group_name][k] = v
                        if not parsed_fields[group_name][k].get('pass', False):
                            parsed_fields[group_name][k]['pass'] = True
                            parsed_fields[group_name][k]['message'] = ''
        return parsed_fields
    
    def save_bre_results(self):
        bre_results = self.crud_bre_results.find_bre_results_by_wi_id(self.document_id)
        if bre_results is None:
            iteration = 1
            self.mongo_client.insert_one({"aria_wi_id": self.document_id, "total_iterations": 1, "iterations": {"1": {"time": self.now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": self.event, "output": "", "passed_rules": "", "not_passed_rules": "", "success": "", "error_message": ""}}})
        else:
            iteration = bre_results['total_iterations'] + 1
            self.mongo_client.update_one(filter={"aria_wi_id": self.document_id}, data={"$set": {"total_iterations": iteration, f"iterations.{iteration}": {"time": self.now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": self.event, "output": "", "passed_rules": "", "not_passed_rules": "", "success": "", "error_message": ""}}})

    def update_parsed_fields_with_execution_rules_output(self, execution_rules_output, group_name):
        # Ensure the group exists in parsed_fields
        if group_name not in self.parsed_fields:
            self.parsed_fields[group_name] = {}
            
        # Updating output json with error messages and values for the specific group
        for k,v in execution_rules_output.items():
            if self.parsed_fields[group_name].get(k, None) == None:
                self.parsed_fields[group_name][k] = {}
            self.parsed_fields[group_name][k]['display'] = v.get('display', True)
            self.parsed_fields[group_name][k]['pass'] = v.get('pass', True)
            self.parsed_fields[group_name][k]['message'] = v.get('message', '')
            self.parsed_fields[group_name][k]['value'] = v.get('value', '')
            #if v.get('coordinates', None):
            if 'coordinates' in v:
                self.parsed_fields[group_name][k]['coordinates'] = v.get('coordinates')


    def run(self):

        try:

            self.parsed_fields = self.get_parsed_fields()
            self.save_bre_results()

            # Process each group separately
            all_passed_rules = {}
            all_not_passed_rules = {}
            
            # Get rules specific to this group's fields
            group_fields = self.parsed_fields.get(self.document_type, {})
            rules = get_rules_json(self.document_type, self.document_id, group_fields, self.mongo_client)

            rules_with_pre_req = []
            for k, v in rules.items():
                if v.get('pre_req', []) != [] and k in self.valid_rules:
                    rules_with_pre_req.append(k)
                
            # Execute rules for this group
            
            execution_rules_output, passed_rules, not_passed_rules = self.execute_rules(rules, rules_with_pre_req)

            print("EXECUTION RULES LOGS")
            print(self.rules_output_log)
            print("***********************")

            print("EXECUTION RULES OUTPUT")
            print(execution_rules_output)
            print("***********************")

            # Update parsed fields for this group
            self.update_parsed_fields_with_execution_rules_output(execution_rules_output, self.document_type)

            # Store results for this group
            all_passed_rules[self.document_type] = passed_rules
            all_not_passed_rules[self.document_type] = not_passed_rules

            # Update event with results
            self.event['parsed_fields'] = self.parsed_fields
            self.event['passed_rules'] = all_passed_rules
            self.event['not_passed_rules'] = all_not_passed_rules
            self.event['valid_rules'] = self.valid_rules

            return self.event
            
        
        except Exception as e:
            print('Error: {}'.format(traceback.format_exc()))
            try:
                self.crud_handler.mark_as_failed(execution_id=self.execution_id)
            except:
                pass
                print('Error updating handler with id {}'.format(self.execution_id))
            return {
                'statusCode': 500,
                'body': json.dumps('Error:'.format(e))
            }