import os
import re
import json
from datetime       import datetime
from vininfo import Vin
from utils.boto3_utils import trigger_lambda_response
import requests
def clean_and_convert(value):
    # Remove all non-numeric characters except `.`
    cleaned_value = re.sub(r"[^\d.]", "", str(value))

    # Ensure there's only one decimal point by keeping the first occurrence
    if cleaned_value.count('.') > 1:
        parts = cleaned_value.split('.')
        cleaned_value = parts[0] + '.' + ''.join(parts[1:])  # Keep first dot, remove others

    return float(cleaned_value) if cleaned_value else ""  # Convert to float safely


def validate_titles_pages_list(text):
    print("Text to validate: ", text)
    if not text:
        return False

    pattern = r"^\d+(,\s*\d+)*$"  
    if re.fullmatch(pattern, text):
        return True
    else:
        print("Invalid format")
        return False

def is_valid_date(date_str):
    pattern = r"^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$"
    return bool(re.match(pattern, date_str))


def check_max_amount(brand, val):
    
    if isinstance(val, str):
        if not val or val.replace(' ','') == '':
            return False
    
    brand = brand.lower()

    if "lexus" in brand:
        return val > 300000
    elif "porsche" in brand:
        return val > 300000
    elif "honda" in brand:
        return val > 300000
    elif "ford" in brand:
        return val > 300000
    elif "lincoln" in brand:
        return val > 300000
    elif "cadillac" in brand:
        return val > 300000
    elif "gm" in brand:
        return val > 300000
    elif "mazda" in brand:
        return val > 300000
    elif "jaguar" in brand:
        return val > 300000
    elif "landrover" in brand:
        return val > 300000


################################################################################################
#
#                       RULES FOR TITLES
#
################################################################################################

def rule_1(parsed_fields):
    
    rows = parsed_fields['vins'].get("rows", {}) or parsed_fields['vins'].get("value", {})
    max_pages = int(parsed_fields.get("pdf_pages", {}).get("value", ""))  if parsed_fields.get('pdf_pages', {}).get('value', "") != "" else ""

    result = {}
    result['vins'] = {'pass': True, 'value': rows, 'display': True}

    all_ok = True
    for k, v in rows.items():
        vin_val = v['cells']['vin']['value'].upper().replace(' ','').replace("O", "0").replace("I", "1").replace("Q", "9")
        make_val = v['cells']['make']['value']
        pages = v['cells']['pages']['value']
        
        if (not vin_val or vin_val == '' or len(vin_val) != 17) or Vin(vin_val).verify_checksum() == False:
            v['cells']['vin'] = {'pass': False, 'value': vin_val, 'display': True, 'message': 'VIN value failed verification. Wrong VIN or malformed.', 'confidence': 'None', "coordinates": v['cells']['vin']['coordinates']}
            all_ok = False
        
        if not make_val or make_val.replace(" ", "") == '':
            v['cells']['make'] = {'pass': False, 'value': make_val, 'display': True, 'message': 'Make val couldnt be empty.', 'confidence': 'None', "coordinates": v['cells']['make']['coordinates']}
            all_ok = False

        if not pages or pages.replace(" ", "") == '' or validate_titles_pages_list(pages) == False:
            v['cells']['pages'] = {'pass': False, 'value': pages, 'display': True, 'message': 'Pages are in an invalid format, format. Example: 1,2,3 ', 'confidence': 'None'}
            all_ok = False

        if validate_titles_pages_list(pages) == True and pages.replace(" ", "") != '':
            num_vals = pages.split(",")
            int_arr = list(map(int, num_vals))
            if max_pages != "":
                if any(x > max_pages for x in int_arr):
                    v['cells']['pages'] = {'pass': False, 'value': pages, 'display': True, 'message': f'At least one value is greater than the maximum of the pages of the document. ({max_pages})', 'confidence': 'None'}
                    all_ok = False


    if not all_ok:
        result['vins']['pass'] = False
        result['vins']['message'] = 'Needs human review'
        result['vins']['value'] = rows

    return result

################################################################################################
#
#                       RULES FOR BOLS
#
################################################################################################


def rule_21(input_data):
    result = {}

    input_data = "" if input_data is None else input_data
    input_data = input_data.split(" ")[0]
    if "-" in input_data:
        vals = input_data.split("-")
        input_data = f"{vals[1]}/{vals[2]}/{vals[0]}"

    result['bol_date'] = {'pass': True, 'value': input_data.replace(' ',''), 'message': "", 'display': True}

    input_data
    if not input_data or input_data.replace(' ','') == '' and not is_valid_date(input_data.replace(' ','')):
        result['bol_date']['pass'] = False
        result['bol_date']['message'] = 'Bol date value not found'
        result['bol_date']['value'] = input_data
        result['bol_date']['coordinates'] = {}


    return result

def rule_22(parsed_fields, crud_vin):

    rows = parsed_fields['bol_vins'].get("rows", {}) or parsed_fields['bol_vins'].get("value", {})

    result = {}
    result['bol_vins'] = {'pass': True, 'value': rows, 'display': True}

    all_ok = True
    vins_found = []
    for k, v in rows.items():
        vin_val = v['cells']['vin']['value'].upper().replace(' ','').replace("O", "0").replace("I", "1").replace("Q", "9")
        vins_found.append(vin_val)
        vin_coords = v['cells']['vin']['coordinates']
        
        v['cells'] = {'vin': {'pass': True, 'value': vin_val, 'display': True, 'confidence': 'None', "coordinates": vin_coords}}      
        if (not vin_val or vin_val == '' or len(vin_val) != 17 or Vin(vin_val).verify_checksum() == False):
            v['cells'] = {'vin': {'pass': False, 'value': vin_val, 'display': True, 'message': 'VIN value failed verification. Wrong VIN or malformed.', 'confidence': 'None', "coordinates": vin_coords}}      
            all_ok = False

    if not all_ok or len(rows.items()) == 0:
        result['bol_vins']['pass'] = False
        result['bol_vins']['message'] = 'Any vin value not found'
        result['bol_vins']['value'] = rows

    result['vins_in_document'] = {'pass': True, 'value': ", ".join(vins_found), 'display': False}
        
    return result

def rule_23(parsed_fields):
    rows = parsed_fields['dates'].get("rows", {}) or parsed_fields['dates'].get("value", {})

    all_dates_ok = True

    result = {}

    if len(list(rows.keys())) > 0:

        k, v = next(iter(rows.items()))  # Get the first key-value pair safely
        first_date = v['cells']['date']['value'].replace(" ", "")
        
        result['bol_date'] = {'pass': True, 'value': first_date, 'display': True, 'message': ''}

        for k,v in rows.items():
            date_val = v['cells']['date']['value'].replace(" ", "")
            print(date_val)
            if date_val not in first_date:
                all_dates_ok = False

        if all_dates_ok == False:
            result['bol_date']['pass'] = False
            result['bol_date']['message'] = f'Multiple dates found'
            result['bol_date']['value'] = ""
            result['bol_date']['coordinates'] = {}

    else:
        result['bol_date'] = {'pass': False, 'value': "", 'display': True, 'message': 'No dates found', 'coordinates': {}}
    
    return result

def rule_24(parsed_fields, crud_vin):

    rows = parsed_fields['bol_vins'].get("rows", {}) or parsed_fields['bol_vins'].get("value", {})

    result = {}
    result['bol_vins'] = {'pass': True, 'value': rows, 'display': True}

    all_ok = True
    for k, v in rows.items():
        vin_val = v['cells']['vin']['value'].upper().replace(' ','').replace("O", "0").replace("I", "1").replace("Q", "9")
        vin_coords = v['cells']['vin']['coordinates']
        vin_data = crud_vin.find_report_row_by_vin(vin_val)
        
        if vin_data is None:
            v['cells'] = {'vin': {'pass': False, 'value': vin_val, 'display': True, 'message': 'VIN not found in DMS', 'confidence': 'None', "coordinates": vin_coords}}
            all_ok = False

    if not all_ok or len(rows.items()) == 0:
        result['bol_vins']['pass'] = False
        result['bol_vins']['message'] = 'VIN not found in DMS'
        result['bol_vins']['value'] = rows

    return result

def construct_address_verifier_rule_25(address_1, valid_address):
    return {
        "provider": "openai",
        "host": "public",
        "llm_model":"gpt4o",
        "prompt": """

            Youre going to verify one address, it could not be the exact same but the that address has to be similar to one of the
            valid address of the stores.

            Return and only return this json with this format.
            return the store name if the first address is similar to its address and NONE if there is no store with a similar address
            {
                "store": "example_store"
            }
                        
            Output Requirements:
            - Provide only the JSON object without any additional text or explanations.
            - Do not include 'json' tags or any special symbols; output should only contain the `{}` characters.

        """,
        "message": f"""
        
            This is the first address:
            {address_1}

            This are the valid address and their stores:
            {valid_address}

        """
    }

def rule_25(parsed_fields, valid_address):

    address_extracted = parsed_fields['address'].get('value') if parsed_fields.get('address', '') else ''

    msg_to_verify =  construct_address_verifier_rule_25(address_extracted, valid_address)
    llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_verify)

    print(f" ****** RESPONSE FROM LLM ****** ")
    print(llm_lambda_response)
    print("********************************************")

    if llm_lambda_response['statusCode'] != 200:
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Error response from llm."})
        }
    
    llm_lambda_response = json.loads(llm_lambda_response['body'])
    llm_lambda_response = llm_lambda_response['message']

    result = {}

    if "none" in llm_lambda_response.lower():
        result['address'] = {'pass': False, 'value': address_extracted, 'display': True}
        result['store'] = {'pass': False, 'value': "", 'message': 'Address didnt pass the verification', 'display': True}
    else:
        store_val = llm_lambda_response[llm_lambda_response.find(": \"") + 3: llm_lambda_response.rfind("\"")]
        result['address'] = {'pass': True, 'value': address_extracted, 'display': True}
        result['store'] = {'pass': True, 'value': store_val, 'display': True}

    return result

def rule_26(input_data):
    result = {}
    result['store'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['store']['pass'] = False
        result['store']['message'] = 'Store couldnt be empty'
        result['store']['value'] = input_data
    return result


################################################################################################
#
#                       RULES FOR INVOICES
#
################################################################################################


def rule_41(input_data):
    result = {}
    result['make'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['make']['pass'] = False
        result['make']['message'] = 'Make value not found'
        result['make']['value'] = input_data
    return result

def rule_42(input_data, vendor, db):
    result = {}
    result['model_code'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['model_code']['pass'] = False
        result['model_code']['message'] = 'Model code value not found'
        result['model_code']['value'] = input_data

    if result['model_code']['pass'] == True and "honda" in vendor.lower():
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code_exists = honda_prices.find_one({"model_code": input_data.replace(" ", "")})
        if model_code_exists is None:
            result['model_code']['pass'] = False
            result['model_code']['message'] = 'No model code found on the pricing guide'
            result['model_code']['value'] = input_data

    return result

def rule_43(input_data):
    result = {}
    result['model_description'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['model_description']['pass'] = False
        result['model_description']['message'] = 'Model description value not found'
        result['model_description']['value'] = input_data
    return result

def rule_44(input_data, vendor):
    result = {}
    result['delivery_charge'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['delivery_charge']['pass'] = False
        result['delivery_charge']['message'] = 'Delivery charge value not found'
        result['delivery_charge']['value'] = input_data

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['delivery_charge']['pass'] = False
        result['delivery_charge']['message'] = 'Delivery charge value is too high.'
        result['delivery_charge']['value'] = input_data

    return result

def rule_45(input_data):
    result = {}
    result['sold_to'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['sold_to']['pass'] = False
        result['sold_to']['message'] = 'Sold to value not found'
        result['sold_to']['value'] = input_data
    return result

def rule_46(input_data):
    vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")

    result = {}
    result['vin'] = {'pass': True, 'value': vin, 'display': True}
    if not input_data or input_data.replace(' ','') == '' or len(vin) != 17 or Vin(vin).verify_checksum() == False:
        result['vin']['pass'] = False
        result['vin']['message'] = 'VIN value failed verification. Wrong VIN or malformed.'
        result['vin']['value'] = input_data
    return result

def rule_47(input_data, vendor):
    result = {}

    value = clean_and_convert(input_data)
    if "honda" in vendor.lower():
        if "." not in str(input_data) and isinstance(value, (int, float)):
            value /= 100        
    

    result['hold_back'] = {'pass': True, 'value': value, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['hold_back']['pass'] = False
        result['hold_back']['message'] = 'Hold back value not found'
        result['hold_back']['value'] = input_data
        result['hold_back']['coordinates'] = {}

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['hold_back']['pass'] = False
        result['hold_back']['message'] = 'Hold back value is too high.'
        result['hold_back']['value'] = input_data

    return result

def rule_48(input_data, vendor):
    result = {}
    result['total_dealer_invoice'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['total_dealer_invoice']['pass'] = False
        result['total_dealer_invoice']['message'] = 'Total dealer invoice value not found'
        result['total_dealer_invoice']['value'] = input_data

    
    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['total_dealer_invoice']['pass'] = False
        result['total_dealer_invoice']['message'] = 'Total dealer invoice value is too high.'
        result['total_dealer_invoice']['value'] = input_data

    return result

def rule_49(input_data, vendor):
    result = {}
    result['domestic_wholesale'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['domestic_wholesale']['pass'] = False
        result['domestic_wholesale']['message'] = 'Domestic wholesale value not found'
        result['domestic_wholesale']['value'] = input_data

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['domestic_wholesale']['pass'] = False
        result['domestic_wholesale']['message'] = 'Domestic wholesale value is too high.'
        result['domestic_wholesale']['value'] = input_data

    return result

def rule_50(input_data, vendor):
    result = {}
    result['total_vehicle_&_options'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}

    if not input_data or input_data.replace(' ','') == '':
        result['total_vehicle_&_options']['pass'] = False
        result['total_vehicle_&_options']['message'] = 'Total vehicle and options value not found'
        result['total_vehicle_&_options']['value'] = input_data

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['total_vehicle_&_options']['pass'] = False
        result['total_vehicle_&_options']['message'] = 'Total vehicle and options value is too high.'
        result['total_vehicle_&_options']['value'] = input_data

    return result

def rule_51(input_data, vendor):
    result = {}
    result['retail_amount'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['retail_amount']['pass'] = False
        result['retail_amount']['message'] = 'Retail amount value not found'
        result['retail_amount']['value'] = input_data

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['retail_amount']['pass'] = False
        result['retail_amount']['message'] = 'Retail amount and options value is too high.'
        result['retail_amount']['value'] = input_data

    return result

def rule_52(input_data):
    
    rows = input_data['reynols_report'].get("rows", {})
    store_value = None

    for k, v in rows.items():
        field_name = v['cells']['field']['value']
        if "store" in field_name.lower():
            store_value = v['cells']['value']['value']

    result = {}
    result['reynols_report'] = {'pass': True, 'value': rows, 'display': True}
    if store_value is None or store_value == '':
        result['reynols_report']['pass'] = False
        result['reynols_report']['message'] = 'Store value not found'
        result['reynols_report']['value'] = rows
    return result

def rule_53(parsed_fields, mapping_fields_row_uuid):

    field_name = "INVOICE AMT"

    rows = parsed_fields['stock_in_values'].get("rows", {})
    value = clean_and_convert(parsed_fields["total_dealer_invoice"].get("value")) if parsed_fields.get('total_dealer_invoice', {}).get('value', "") != "" else ""
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': 'INVOICE AMT', 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}
        }

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = 'Invoice amount value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
            'field': {'pass': True, 'value': 'INVOICE AMT', 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': "Value could be calculated.", 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}
            }

    return result

def rule_54(parsed_fields, mapping_fields_row_uuid):

    field_name = "INVENTORY AMT"

    rows = parsed_fields['stock_in_values'].get("rows", {})
    value = clean_and_convert(parsed_fields["total_dealer_invoice"].get("value")) if parsed_fields.get('total_dealer_invoice', {}).get('value', "") != "" else ""

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': 'INVENTORY AMT', 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = 'Inventory amount value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
            'field': {'pass': True, 'value': 'INVENTORY AMT', 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': "Value could be calculated.", 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}
            }
        
    return result

def rule_55(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 1 AMT DT"

    value = ""
    if "honda" in vendor:
        hold_back_val = parsed_fields.get('hold_back', {}).get('value', "")
        hold_back_val_nr = clean_and_convert(hold_back_val)
        if "." not in str(hold_back_val) and isinstance(hold_back_val_nr, (int, float)):
            hold_back_val_nr /= 100        

        value = hold_back_val_nr
    elif "ford" in vendor or "lincoln" in vendor:
        total_vehicle_options_and_other = parsed_fields.get("total_vehicle_&_options", {}).get("value", "")
        value = round((clean_and_convert(total_vehicle_options_and_other) * 0.01), 2) if total_vehicle_options_and_other != "" else ""
    elif "cadillac" in vendor:
        model_description = str(parsed_fields['model_description'].get('value')).lower().replace(" ", "") if parsed_fields.get('model_description', '') else ''
        if "lyriq" in model_description:
            value = round((clean_and_convert(parsed_fields["msrp_amount"].get("value"))*0.015), 2) if parsed_fields.get('msrp_amount', {}).get('value', "") != "" else ""
        else:
            value = round((clean_and_convert(parsed_fields["total_dealer_invoice"].get("value"))*0.043/360) * 126, 2) if parsed_fields.get('total_dealer_invoice', {}).get('value', "") != "" else ""

    elif "gm" in vendor:
        pass       
    elif "mazda" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = round(clean_and_convert(hold_back), 2)  if hold_back != "" else ""

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]
    
    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result

def rule_56(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 1 ACCT1-DT"
  
    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows
    
        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_57(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 1 ACCT 2-CR"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}


    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_58(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 2 AMT DT"

    value = ""
    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }
        
        model_code_data = honda_prices.find_one(filter)
        if model_code_data:
            value = model_code_data["DMA"]

    elif "ford" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = (clean_and_convert(hold_back)) if hold_back != "" else ""
    elif "gm" in vendor:
        pass       
    elif "mazda" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = round(clean_and_convert(hold_back), 2)  if hold_back != "" else ""



    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]
    
    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows


        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_59(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 2 ACCT 1-DT"
    
    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]


    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows


        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_60(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 2 ACCT 2-CR"
    
    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows


        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_61(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 3 AMT DT"

    value = ""

    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }

        model_code_data = honda_prices.find_one(filter)
        if model_code_data:
            value = model_code_data["FPA"]
    
    elif "mazda" in vendor:
        dealer_invoice = parsed_fields.get("total_dealer_invoice", {}).get("value", "")
        value = round((clean_and_convert(dealer_invoice) * 0.5), 2)  if dealer_invoice != "" else ""

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows


        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_62(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 3 ACCT 1-DT"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows


        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_63(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 3 ACCT 2-CR"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows
        
        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result


def rule_64(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 4 AMT DT"
    value = ""
    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }

        model_code_data = honda_prices.find_one(filter)
    
        print(model_code_data)
        if model_code_data:
            value = model_code_data["HTB"]

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_65(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 4 ACCT 1-DT"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = 'MFR 4 ACCT 1-DT value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_66(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "MFR 4 ACCT 2-CR"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    value = ""
    
    if isinstance(store_brand_vals, dict):    
        value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_67(parsed_fields,mapping_fields_row_uuid, store, vendor):

    field_name = "VMS ENTRY"

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]


    value = ""
    if "lexus" in vendor:
        value = round(clean_and_convert(parsed_fields["wholesale_finance_reserve"].get("value")) / 0.02, 2) if parsed_fields.get("wholesale_finance_reserve", {}).get("value", "") != "" else ""
    elif "porsche" in vendor:
        
        cocar = str(parsed_fields['cocar'].get('value')) if parsed_fields.get('cocar', '') else ''
        is_cocar = False if not cocar or cocar.replace(' ','') == '' or "false" in cocar.lower() else True

        if is_cocar:
            value = ""
            rows[row_uuid] = {}
            rows[row_uuid]['cells'] = {
                'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
                'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            result = {}
            result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
            return result
        
        else:
            value = clean_and_convert(parsed_fields["retail_amount"].get("value")) if parsed_fields.get("retail_amount", {}).get("value", "") != "" else ""
 
    elif "mazda" in vendor:
        value = clean_and_convert(parsed_fields["retail_amount"].get("value")) if parsed_fields.get("retail_amount", {}).get("value", "") != "" else ""

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_68(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "STORE"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}


    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_69(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "BRAND"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_70(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "PREFIX"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_71(parsed_fields, mapping_fields_row_uuid):

    field_name = "REFERENCE"

    stock_number_val = str(parsed_fields['stock_number'].get('value')) if parsed_fields.get('stock_number', '') else ''

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': stock_number_val, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if stock_number_val == "" or str(stock_number_val).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': stock_number_val, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_72(parsed_fields, mapping_fields_row_uuid):

    field_name = "DATE"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}
    

    date = parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None))  else ''
    ro_date = parsed_fields['ro_date'].get('value') if "None" not in str(parsed_fields.get('ro_date', {}).get("value", None)) else ''

    if not date or date.replace(' ','') == '':        
        date = ro_date

    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': date.split(" ")[0], 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}

    return result

def rule_73(parsed_fields, mapping_fields_row_uuid, store_brand_vals):

    field_name = "VENDOR #"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    value = store_brand_vals[field_name]

    row_uuid = mapping_fields_row_uuid[field_name]
    
    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "" or str(value).lower() ==  "nan":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} value not found'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be get from DB','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_74(ro_date, bol_date, days_passed):
    key_date = "ro_date"
    date = ro_date

    result = {}
    result[key_date] = {'pass': True, 'value': date, 'display': True}

    if not date or date.replace(' ','') == '':
        result[key_date]['pass'] = False

        if days_passed > 60 and (bol_date is None or bol_date.replace(" ", "") == ""):
            result[key_date]['message'] = '60 days has been passed since the invoice was read and date value not found for ro date'

        result[key_date]['value'] = date

    return result

def rule_75(parsed_fields, mapping_fields_row_uuid, vendor):

    field_name = "HOLDBACK AMT"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}
    
    value = clean_and_convert(parsed_fields['hold_back'].get("value", ""))
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': clean_and_convert(value), 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}

    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be mapped','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_76(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 1 AMT CR"
    

    value = ""
    if "lexus" in vendor:
        value = -clean_and_convert(parsed_fields["wholesale_finance_reserve"].get("value")) if parsed_fields.get('wholesale_finance_reserve', {}).get('value', "") != "" else ""
    elif "honda" in vendor:
        hold_back_val = parsed_fields.get('hold_back', {}).get('value', "")
        hold_back_val_nr = clean_and_convert(hold_back_val)
        if "." not in str(hold_back_val) and isinstance(hold_back_val_nr, (int, float)):
            hold_back_val_nr /= 100        

        value = -hold_back_val_nr    
    elif "ford" in vendor or "lincoln" in vendor:
        total_vehicle_options_and_other = parsed_fields.get("total_vehicle_&_options", {}).get("value", "")
        value = -round((clean_and_convert(total_vehicle_options_and_other) * 0.01), 2) if total_vehicle_options_and_other != "" else ""
    elif "cadillac" in vendor:
        model_description = str(parsed_fields['model_description'].get('value')).lower().replace(" ", "") if parsed_fields.get('model_description', '') else ''
        if "lyriq" in model_description:
            value = -round((clean_and_convert(parsed_fields["msrp_amount"].get("value"))*0.015), 2) if parsed_fields.get('msrp_amount', {}).get('value', "") != "" else ""
        else:
            value = -round((clean_and_convert(parsed_fields["total_dealer_invoice"].get("value"))*0.043/360) * 126, 2) if parsed_fields.get('total_dealer_invoice', {}).get('value', "") != "" else ""
    elif "gm" in vendor:
        pass       
    elif "mazda" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = -round(clean_and_convert(hold_back), 2) if hold_back != "" else ""

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result

def rule_77(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 2 AMT CR"

    value = ""
    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }

        model_code_data = honda_prices.find_one(filter)
        if model_code_data:
            value = - model_code_data["DMA"]

    elif "ford" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = -clean_and_convert(hold_back) if hold_back != "" else ""
    elif "gm" in vendor:
        pass       
    elif "mazda" in vendor:
        hold_back = parsed_fields.get("hold_back", {}).get("value", "")
        value = -round(clean_and_convert(hold_back), 2)  if hold_back != "" else ""

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result


def rule_78(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 3 AMT CR"

    value = ""
    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }

        model_code_data = honda_prices.find_one(filter)
        if model_code_data:
            value = - model_code_data["FPA"]
    
    elif "mazda" in vendor:
        dealer_invoice = parsed_fields.get("total_dealer_invoice", {}).get("value", "")
        value = -round((clean_and_convert(dealer_invoice) * 0.5), 2)  if dealer_invoice != "" else ""

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result


def rule_79(parsed_fields, mapping_fields_row_uuid, vendor, db, rules_result_value=None):

    field_name = "MFR 4 AMT CR"

    value = ""
    if "honda" in vendor:
        color_upcharge_fee = rules_result_value
        honda_prices = db.get_collection("hon_pricing_sheet")
        model_code = parsed_fields["model_code"].get("value").replace(" ", "")

        filter = {"model_code": model_code}
        if not color_upcharge_fee:
            filter["Model description"] = { "$not": { "$regex": "premium", "$options": "i" } }
        else:
            filter["Model description"] = { "$regex": "premium", "$options": "i" }

        model_code_data = honda_prices.find_one(filter)
        if model_code_data:
            value = - model_code_data["HTB"]

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result

def rule_80(parsed_fields, days_passed):

    date = parsed_fields['ro_date'].get('value') if "None" not in str(parsed_fields.get('ro_date', {}).get("value", None))  else ''
    
    bol_date = parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None))  else ''

    result = {}
    result['ro_date'] = {'pass': True, 'value': date, 'display': True}

    if (not date or date.replace(' ','') == '') and days_passed > 60 and (not bol_date or bol_date.replace(' ','') == ''):
        result['ro_date']['pass'] = False
        result['ro_date']  ['value'] = date
        result['ro_date']['message'] = 'No date found after 60 days on hold'

    return result


def rule_81(parsed_fields, vin_report_value):
    vin_extracted = parsed_fields['vin'].get('value') if parsed_fields.get('vin', '') else ''
    vin_extracted = vin_extracted.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")

    result = {}
    result['vin'] = {'pass': True, 'value': vin_extracted, 'display': True, "message": ""}

    if vin_extracted != vin_report_value:
        result['vin']['pass'] = False
        result['vin']['value'] = vin_extracted
        result['vin']['message'] = 'VIN extracted doesnt match with the VIN of the report.'
        
    return result



def rule_82(parsed_fields, days_passed):

    date = parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None))  else ''
    
    ro_date = parsed_fields['ro_date'].get('value') if "None" not in str(parsed_fields.get('ro_date', {}).get("value", None))  else ''
    
    result = {}
    result['bol_date'] = {'pass': True, 'value': date, 'display': True}
    
    if (not date or date.replace(' ','') == '') and days_passed > 60 and (not ro_date or ro_date.replace(' ','') == ''):
        result['bol_date']['pass'] = False
        result['bol_date']  ['value'] = date
        result['bol_date']['message'] = 'No date found after 60 days on hold'

    return result


def construct_address_verifier_rule_83(address_1, address_2):
    return {
        "provider": "openai",
        "host": "public",
        "llm_model":"gpt-4.1-mini",
        "prompt": """

            Youre going to verify two addresses, it could not be the exact same but they have to look similar.


            Return and only return this json with this format.
            Use true if both of them looks similar and false if not and false if you are not sure
            {
                "similar": "<true or false>"
            }
                        
            Output Requirements:
            - Provide only the JSON object without any additional text or explanations.
            - Do not include 'json' tags or any special symbols; output should only contain the `{}` characters.

        """,
        "message": f"""
        
            This is the first address:
            {address_1}

            This is the second address:
            {address_2}

        """
    }

def rule_83(parsed_fields, store_brand_vals):

    sold_to_value = parsed_fields['sold_to'].get('value') if parsed_fields.get('sold_to', '') else ''

    result = {}
    result['sold_to'] = {'pass': False, 'value': sold_to_value, 'message': 'Sold to value could be verificated', 'display': True}

    if not isinstance(store_brand_vals, dict):
        return store_brand_vals
    
    address_val = store_brand_vals['ADDRESS']

    msg_to_verify =  construct_address_verifier_rule_83(sold_to_value, address_val)
    llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_verify)

    print(f" ****** RESPONSE FROM LLM ****** ")
    print(llm_lambda_response)
    print("********************************************")

    if llm_lambda_response['statusCode'] != 200:
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Error response from llm."})
        }
    
    llm_lambda_response = json.loads(llm_lambda_response['body'])
    llm_lambda_response = llm_lambda_response['message']
    
    if "true" in llm_lambda_response.lower():
        result['sold_to'] = {'pass': True, 'value': sold_to_value, 'display': True}
    
    if "false" in llm_lambda_response.lower():
        result['sold_to'] = {'pass': True, 'value': sold_to_value, 'message': 'Address didnt pass the verification', 'display': True}

    return result


def rule_84(bol_date, ro_date, days_passed):
    key_date = "bol_date"
    date = bol_date

    result = {}
    result[key_date] = {'pass': True, 'value': date, 'display': True}

    if not date or date.replace(' ','') == '':         
        result[key_date]['pass'] = False

        if days_passed > 60 and (ro_date is None or ro_date.replace(" ", "") == ""):
            result[key_date]['message'] = '60 days has been passed since the invoice was read and date value not found'

        result[key_date]['value'] = ""

    return result


def rule_85(input_data):
    result = {}
    result['stock_number'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['stock_number']['pass'] = False
        result['stock_number']['message'] = 'Stock number value not found'
        result['stock_number']['value'] = input_data
    return result

def rule_86(input_data):
    result = {}
    result['store'] = {'pass': True, 'value': input_data, 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['store']['pass'] = False
        result['store']['message'] = 'Store amount value not found'
        result['store']['value'] = input_data
    return result

def rule_87(parsed_fields, mapping_fields_row_uuid, vin):
    field_name = "VIN"

    rows = parsed_fields['stock_in_values'].get("rows", {})
    
    row_uuid = mapping_fields_row_uuid[field_name]
    
    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True,'value': vin, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
    if vin == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be calculated','value': vin, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        result['stock_in_values']['value'] = rows

    return result

def rule_88(input_data):
    result = {}
    result['wholesale_finance_reserve'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['wholesale_finance_reserve']['pass'] = False
        result['wholesale_finance_reserve']['message'] = 'Wholesale finance reserve value not found'
        result['wholesale_finance_reserve']['value'] = input_data
    return result

def rule_89(input_data):
    result = {}
    result['cocar'] = {'pass': True, 'value': False if not input_data or input_data.replace(' ','') == '' or "false" in input_data.lower() else True, 'display': True}
    return result

def construct_electric_verifier_rule_90(store, brand, model_code, model_description):
    return {
        "provider": "openai",
        "host": "public",
        "llm_model":"gpt4o",
        "prompt": """

             You are an automotive expert specializing in electric vehicles. Based on the given store, brand, model number, and model description, determine if the vehicle is fully electric. The response should be strictly:
                
                •	{ "electric": true } if the vehicle is 100% electric.
                •	{ "electric": false } if it is a hybrid, gasoline, diesel, or any other non-electric vehicle.

            Use the latest manufacturer specifications to verify. Exclude hybrids, plug-in hybrids, and any non-electric vehicles. If the model exists in both electric and non-electric versions, default to false unless explicitly confirmed as electric.
                        
            Output Requirements:
            - Provide only the JSON object without any additional text or explanations.
            - Do not include 'json' tags or any special symbols; output should only contain the `{}` characters.

        """,
        "message": f"""
        
            * The store is {store}
            * The brand is {brand}
            * The model code is {model_code}
            * The model description is {model_description}
            
        """
    }

def rule_90(parsed_fields, store, brand, vin_report_value):

    model_code = parsed_fields.get('model_code', {}).get('value', "") if parsed_fields.get('model_code', '') else ''
    model_description = parsed_fields.get('model_description', {}).get('value', "") if parsed_fields.get('model_description', '') else ''
    electric_val = None
    result = {}
    result['electric_vehicle'] = {'pass': True, 'value': False, 'display': True}

    response = requests.get(f"https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVinValuesExtended/{vin_report_value}?format=json")
    try:
        data = response.json()["Results"][0]

        if data['FuelTypePrimary'] == "Electric" and data['FuelTypeSecondary'] == "":
            electric_val = True 
        elif data['FuelTypePrimary'] == "" and data['FuelTypeSecondary'] == "":
            electric_val = None
        else:
            electric_val = False
        
    except Exception:

        msg_to_verify =  construct_electric_verifier_rule_90(store, brand, model_code, model_description)
        llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_verify)

        print(f" ****** RESPONSE FROM LLM ****** ")
        print(llm_lambda_response)
        print("********************************************")

        if llm_lambda_response['statusCode'] != 200:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from llm."})
            }
        
        llm_lambda_response = json.loads(llm_lambda_response['body'])
        llm_lambda_response = llm_lambda_response['message']
        
        electric_val = True if "true" in llm_lambda_response.lower() else False if "false" in llm_lambda_response.lower() else None
    
    if electric_val:
        result['electric_vehicle'] = {'pass': True, 'value': True, 'display': True}
    elif electric_val == False:
        result['electric_vehicle'] = {'pass': True, 'value': False, 'display': True}
    else:
        result['electric_vehicle'] = {'pass': False, 'value': "", 'display': True, "message": "Couldnt determine if the vehicle is electric or not."}


    return result

def rule_91(input_data):
    result = {}

    input_data = "" if input_data is None else input_data
    input_data = input_data.split(" ")[0]
    if "-" in input_data:
        vals = input_data.split("-")
        input_data = f"{vals[1]}/{vals[2]}/{vals[0]}"

    result['invoice_date'] = {'pass': True, 'value': input_data.replace(' ',''), 'message': "", 'display': True}

    input_data
    if not input_data or input_data.replace(' ','') == '' and not is_valid_date(input_data.replace(' ','')):
        result['invoice_date']['pass'] = False
        result['invoice_date']['message'] = 'Invoice date value not found'
        result['invoice_date']['value'] = input_data
        result['invoice_date']['coordinates'] = {}


    return result

def rule_92(parsed_fields, mapping_fields_row_uuid, vendor):

    field_name = "PDI ALLOWANCE AMT"

    rows = parsed_fields['stock_in_values'].get("rows", {})

    result = {}
    result['stock_in_values'] = {'pass': False, 'value': rows, 'message': f'{field_name} value not found'}
    
    if "lexus" in vendor:
        value = clean_and_convert(parsed_fields['wholesale_finance_reserve'].get("value", ""))
    
    row_uuid = mapping_fields_row_uuid[field_name]

    rows[row_uuid] = {}
    rows[row_uuid]['cells'] = {
        'field': {'pass': True, 'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': True, 'value': clean_and_convert(value), 'display': True, 'confidence': 'None', 'coordinates': {}}}

    result = {}
    result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}

    if value == "":
        result['stock_in_values']['pass'] = False
        result['stock_in_values']['message'] = f'{field_name} cant be calculated'
        result['stock_in_values']['value'] = rows

        rows[row_uuid]['cells'] = {
        'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
        'value': {'pass': False, 'message': 'Value couldnt be mapped','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

    return result

def rule_93(input_data):
    result = {}
    is_porsche_experience_delivery =  False if not input_data or input_data.replace(' ','') == '' or "false" in input_data.lower() else True
    result['porsche_experience_delivery'] = {'pass': is_porsche_experience_delivery, 'value': is_porsche_experience_delivery, 'display': True, 'message': '', 'coordinates': {}}
    return result

def rule_94(input_data, vendor):
    result = {}
    result['msrp_amount'] = {'pass': True, 'value': clean_and_convert(input_data), 'display': True}
    if not input_data or input_data.replace(' ','') == '':
        result['msrp_amount']['pass'] = False
        result['msrp_amount']['message'] = 'MSRP amount value not found'
        result['msrp_amount']['value'] = input_data

    if check_max_amount(vendor, clean_and_convert(input_data)):
        result['msrp_amount']['pass'] = False
        result['msrp_amount']['message'] = 'MSRP amount value is too high.'
        result['msrp_amount']['value'] = input_data

    return result

def rule_95(input_data):
    result = {}
    result['lccs_car'] = {'pass': True, 'value': False if not input_data or input_data.replace(' ','') == '' or "false" in input_data.lower() else True, 'display': True}
    return result

def rule_96(input_data):
    result = {}
    result['color_upcharge_fee'] = {'pass': True, 'value': False if not input_data or input_data.replace(' ','') == '' or "false" in input_data.lower() else True, 'display': True, 'coordinates': {}}
    return result