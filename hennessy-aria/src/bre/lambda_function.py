import os
import json
import traceback
from botocore.exceptions    import ClientError
from datetime               import datetime
from bre_files.bre import Bre
from post_bre.post_bre import PostBREProcessor
from utils.boto3_utils            import get_secret

def lambda_handler(event, context):


    # Retrieve input data
    print(event)
    

    if event is None or event is {}:
        raise ValueError("Event empty. Skipping...")
    
    mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)


    bre = Bre(event, mongo_uri)
    bre_response = bre.run()

    print("********** BRE RESPONSE **********")
    print(bre_response)
    print("*********************************")

    post_bre_processor = PostBREProcessor(bre_response, mongo_uri)
    response = post_bre_processor.execute()

    print("********** POST BRE RESPONSE **********")
    print(response)
    print("*********************************")
    
    return response


        

        



    