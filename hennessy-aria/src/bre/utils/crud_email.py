import os
import datetime

from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudEmails:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='emails'
    
    def find_email_by_id(self, id):
        query = {"email_id": id}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
   
    def __del__(self):
        self.mongo.close_connection()