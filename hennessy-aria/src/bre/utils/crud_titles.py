# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudTitles:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = "title"

    def insert_title(self, uuid, concated_title, images, title_file, aria_wi_id):
        """
        This function inserts a folio into the database.
        """

        title_document = {
            "title_id": str(uuid),
            "images_of_title": images,
            "path": concated_title,
            "from_file": title_file,
            "aria_wi_id": aria_wi_id,
            "read_at": datetime.now(),
            "status_history": ["Pending"]
        }
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.insert_one(title_document)
    
    def find_title_by_wi_id(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"aria_wi_id": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def update_title_by_wi_id(self, wi_id, data_to_set, data_to_push):
        query = {"aria_wi_id": wi_id}
        data_to_set['updated_at'] = datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)

    def __del__(self):
        self.mongo.close_connection()