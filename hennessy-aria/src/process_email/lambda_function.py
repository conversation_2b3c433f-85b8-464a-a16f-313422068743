# This module contains the main Lambda function that processes incoming emails and creates folios in the
# ARIA Case Management app.

import json
import traceback
from logger         import Logger
from email_processer_utils.bol_processer import BolProcesser
from email_processer_utils.invoice_processer import InvoiceProcesser

logger_class = Logger()
logger = logger_class.get_root_logger()

def return_error(status, message):
    logger.error(message)
    return {
        'statusCode': status,
        'body': json.dumps({'message': message})
    }

def lambda_handler(event, context):
    """
    This method is the entry point for the AWS Lambda function that processes incoming emails. It
    receives an email as an event, extracts the attachments, and creates as many folios as attachments
    are found. It will create the workitems in ARIA. It will also insert the folios details into the
    MongoDB database.
    """

    print(event)
    
    stage = event.get("stage", None)
    if stage is None or stage == "":
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage provided!")
            }
        }
    

    try:
        # Extract the email from the event
        email_id = event['email_id']

    except Exception as e:
        return return_error(400, f"Error getting email id from event: {str(e)}")
    
    logger.info(f"Processing email {email_id}")

    try:
        processer = None
        if stage == "post-inventory":
            processer = BolProcesser(logger_class, email_id, stage)
        elif stage == "used-cars":
            processer = InvoiceProcesser(logger_class, email_id, stage)

        return processer.run()
    except Exception as e:
        print(f"Error when processing Email: {traceback.format_exc()}")
        return {
            'statusCode': 500,
            'body': json.dumps({"message": f"Error when processing Email: {e}"})
        }


    
