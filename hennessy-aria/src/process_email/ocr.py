import time
from enum import Enum
import boto3
from botocore.client import Config
"""
This class contains all the related to the OCR operations
that we need to process a document in Textract
"""

config = Config(
    retries={
        'max_attempts': 30,  # Number of retry attempts
        'mode': 'standard'  # Retry mode (standard or adaptive)
    }
)


class OCRStatus(Enum):
    pending = 0
    completed = 1
    failed = 2
    not_applicable = 3
    retry = 4
    initial_failure = 5


class Ocr():

    def __init__(self, textract_adapter_details):
        
        self.textract_adapter_details = textract_adapter_details

        self.s3 = boto3.client(
            's3',
            config=Config(signature_version='s3v4'), 
            #aws_access_key_id = self.textract_adapter_details['aws_access_key_id'],
            #aws_secret_access_key = self.textract_adapter_details['aws_secret_access_key'],
            #aws_session_token = self.textract_adapter_details['aws_session_token'],
            region_name = self.textract_adapter_details['region_name']
        )        
        self.textract = boto3.client(
            'textract',
            config=config,
            #aws_access_key_id = self.textract_adapter_details['aws_access_key_id'],
            #aws_secret_access_key = self.textract_adapter_details['aws_secret_access_key'],
            #aws_session_token = self.textract_adapter_details['aws_session_token'],
            region_name = self.textract_adapter_details['region_name']
        )
        self.sns = boto3.client(
            'sns',
            #aws_access_key_id = self.textract_adapter_details['aws_access_key_id'],
            #aws_secret_access_key = self.textract_adapter_details['aws_secret_access_key'],
            #aws_session_token = self.textract_adapter_details['aws_session_token'],
            region_name = self.textract_adapter_details['region_name']
        )


    def async_call_textract(self, file_path, file_name):
        """
        Here we send the document to textract and return
        the job id generated on the textract api and the
        ocr status set to pending (still to be processed)
        """
        file_path += "/" + file_name
        textract_response = self.textract.start_document_text_detection(
            DocumentLocation={
                'S3Object' : {
                    'Bucket' : self.textract_adapter_details['bucket_name'],
                    'Name' : file_path
                }
            }
        )
        
        job_id = textract_response['JobId']

        if not job_id:
            raise Exception("Job initialization failed.")

        return job_id, OCRStatus.pending.value
    
    def get_response(self, job_id, next_token = ""):
        """
        Here we try to ask for the ocr status of the job id
        of the document, to see if there is a new status
        and how it goes
        """

        if next_token != "":
            #job_id_response = self.textract.get_expense_analysis(JobId=job_id, NextToken=next_token)
            job_id_response = self.textract.get_document_text_detection(JobId=job_id, NextToken=next_token)
        else:
            #job_id_response = self.textract.get_expense_analysis(JobId=job_id)
            job_id_response = self.textract.get_document_text_detection(JobId=job_id)


        job_status = job_id_response.get("JobStatus", "")
        next_token = job_id_response.get("NextToken", "")
        
        ocr_status = OCRStatus.pending.value

        if job_status == "SUCCEEDED":
            ocr_status = OCRStatus.completed.value
        elif job_status == "PARTIAL_SUCCESS":
            ocr_status = OCRStatus.retry.value
        elif job_status == "FAILED":
            ocr_status = OCRStatus.failed.value

        return ocr_status, job_id_response, next_token

    
    def get_full_page_string(self, textract_response, only_string = False):
        """
        Given the textract response we fill the complete_document_text
        only with the word block that are contained in the textract
        response
        """

        just_blocks = []
        word_blocks = []

        for response in textract_response:
            if response.get('ExpenseDocuments', ''):
                for expense in response['ExpenseDocuments']:
                    just_blocks.extend(expense['Blocks'])
            else:
                just_blocks = response['Blocks']

            
            max_page = 0
            idx = 0
            mega_string = ""

            for block in just_blocks:
                if block['BlockType'] == 'WORD':

                    mega_string += block['Text'] + " "

                    word_data = {
                        "word": block['Text'],
                        "Id": idx
                    }

                    idx += 1

                    word_blocks.append(word_data)

        
        return word_blocks, mega_string
    