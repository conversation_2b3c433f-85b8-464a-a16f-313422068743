# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret, trigger_lambda

class CrudEmails:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='emails'
        )
        self.move_email_lambda = os.environ['MOVE_EMAIL_LAMBDA']

    def get_first_address(self, recipients):
        if recipients and isinstance(recipients, list) and len(recipients) > 0:
            return recipients[0].get('emailAddress', {}).get('address')
        return None

    def insert_email(self, email, attachments, folder_name):
        """
        This function inserts an email into the database.
        """

        email_document = {
            "email_id": email['internetMessageId'],
            "email_doc_id": email['id'],
            "attachments": [
                {
                    "attachment_name": item['name'],
                    "attachment_id": item['id']
                } for item in attachments
            ],
            "createdDateTime": email['createdDateTime'],
            "lastModifiedDateTime": email['lastModifiedDateTime'],
            "sender": email['sender']['emailAddress']['address'],
            "receiver": self.get_first_address(email.get('toRecipients')),
            "receiver_cc": self.get_first_address(email.get('ccRecipients')),
            "body": email['body']['content'],
            "receivedDateTime": email['receivedDateTime'],
            "subject": email['subject'],
            "read_at": datetime.datetime.now(),
            "updated_at": datetime.datetime.now(),
            "status": "pending",
            "processed_attachments": [],
            "not_applicable_attachments": [],
            "failed_attachments": [],
            "processed_at": None,
            "error": None,
            "folder_name": folder_name,
            "folder_history": [folder_name]
        }

        self.mongo.insert_one(email_document)
        return email_document

    def find_email_by_id(self, email_id):
        """
        This function finds an email by its email ID.
        """
        query = {"email_id": email_id}
        return self.mongo.find_one(query)

    def complete_without_attachments(self, email_id, folder_name, user_id):
        """
        This function completes an email without attachments.
        """
        # Move the email to the unsorted folder
        if os.getenv('MOVE_EMAILS', "False") == "True":
            try:
                trigger_lambda(
                self.move_email_lambda,
                {
                    'action': 'move_email',
                    'email_id': email_id,
                    'destination_folder': folder_name,
                    'user_id': user_id
                }
                )
            except Exception as e:
                print(f"Error moving email to unsorted folder: {str(e)}")
        query = {"email_id": email_id}
        update = {
            "$set": {
                "status": "processed",
                "processed_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now(),
                "folder_name": folder_name
            },
            "$push": {
                "folder_history": folder_name
            }
        }

        return self.mongo.find_one_and_update(query, update) 

    def processed_attachment(self, email_id, attachment_id):
        """
        This function updates the status of a processed attachment.
        """
        query = {"email_id": email_id}
        update = {
            "$push": {
                "processed_attachments": {
                    "attachment_id": attachment_id,
                    "processed_at": datetime.datetime.now()
                }
            },
            "$set": {
                "updated_at": datetime.datetime.now()
            }
        }

        return self.mongo.find_one_and_update(query, update)

    def not_applicable_attachment(self, email_id, attachment_id):
        """
        This function updates the status of a not applicable attachment.
        """
        query = {"email_id": email_id}
        update = {
            "$push": {
                "not_applicable_attachments": attachment_id
            },
            "$set": {
                "updated_at": datetime.datetime.now()
            }
        }

        return self.mongo.find_one_and_update(query, update)

    def failed_attachment(self, email_id, attachment_id, error):
        """
        This function updates the status of a failed attachment.
        """
        query = {"email_id": email_id}
        update = {
            "$push": {
                "failed_attachments": {
                    "attachment_id": attachment_id,
                    "failed_at": datetime.datetime.now(),
                    "error": error
                }
            },
            "$set": {
                "updated_at": datetime.datetime.now()
            }
        }

        return self.mongo.find_one_and_update(query, update)

    def update_email_status(self, email_id, target_folder=None, error=None, user_id=None):
        """
        This function updates the status of an email.
        """
        query = {"email_id": email_id}
        if error:
            update = {
                "$set": {
                    "status": "failed",
                    "error": error,
                    "updated_at": datetime.datetime.now(),
                    "processed_at": datetime.datetime.now()
                }
            }
        else:
            # Retrieve the email from the database
            email = self.mongo.find_one(query)

            # Check if all attachments have been processed
            available_attachments = [att['attachment_id'] for att in email['attachments']]
            processed_attachments = [att['attachment_id'] for att in email['processed_attachments']]
            not_applicable_attachments = email['not_applicable_attachments']

            # Every attachment was not applicable
            if set(available_attachments) == set(not_applicable_attachments):
                unsorted_folder = os.environ['UNSORTED_FOLDER']
                return self.complete_without_attachments(email_id, unsorted_folder, user_id)

            # Every attachment was processed
            elif set(available_attachments) == set(processed_attachments + not_applicable_attachments):
                update = {
                    "$set": {
                        "status": "processed",
                        "processed_at": datetime.datetime.now(),
                        "updated_at": datetime.datetime.now()
                    }
                }

            # Some attachments were not processed
            else:
                # They failed to process
                if email['failed_attachments']:
                    update = {
                        "$set": {
                            "status": "incomplete",
                            "updated_at": datetime.datetime.now(),
                            "error": "Some attachments failed to process",
                            "processed_at": datetime.datetime.now()
                        }
                    }
                # There is an inconsistency
                else:
                    update = {
                        "$set": {
                            "status": "incomplete",
                            "updated_at": datetime.datetime.now(),
                            "error": "Some attachments were not processed",
                            "processed_at": datetime.datetime.now()
                        }
                    }
        
        if target_folder:
            # Move the email to the target folder
            if os.getenv('MOVE_EMAILS', "False") == "True":
                try:
                    trigger_lambda(
                        self.move_email_lambda,
                        {
                            'action': 'move_email',
                            'email_id': email_id,
                            'destination_folder': target_folder,
                            'user_id': user_id
                        }
                    )
                except Exception as e:
                    print(f"Error moving email to processed folder: {str(e)}")
            update['$set']['folder_name'] = target_folder
            update['$push'] = {"folder_history": target_folder}
        return self.mongo.find_one_and_update(query, update)

    def __del__(self):
        self.mongo.close_connection()
