import fitz  # PyMuPDF
from datetime import datetime
from boto3_utils import download_file_from_s3, post_to_s3, trigger_lambda_response
import json
import os

class PdfHandler():
    def __init__(self, event):
        self.event = event
        try:
            self.action = event['action']
        except:
            print("An error has occurred, action needed not found in the event!")
            return {
                "statusCode": 404,
                "response": "An error has occurred, action needed not found in the event!"
            }
        
    def pdf_to_images(self, pdf_path, output_image_prefix, rotations = [], scale_factor=4.5, only_extract_pages = []):
        """
        Converts each page of a multi-page PDF to a high-quality image with optional rotation and scaling.

        Args:
            pdf_path (str): Path to the PDF file.
            output_image_prefix (str): Prefix for naming output images (e.g., "output_page_").
            rotation_angle (float): The angle to rotate the image (default is 0).
            scale_factor (float): The factor by which to scale the image (default is 2 for better quality).

        Returns:
            None
        """
        print(" ***** CONVERTING PDF TO IMAGES ***** ")
        image_paths = []
        try:
            # Open the PDF document
            doc = fitz.open(pdf_path)
            
            # Iterate through all the pages of the PDF
            iterate_through = [x for x in range(len(doc))]

            if only_extract_pages != []:
                iterate_through = only_extract_pages

            for page_num in iterate_through:
                # Get the specified page
                page = doc.load_page(page_num)
                
                # Create a Matrix for rotation and scaling
                matrix = fitz.Matrix(scale_factor, scale_factor)  # Scaling factor for higher resolution
                if rotations != []:
                    if rotations[page_num] != 0:
                        matrix.prerotate(rotations[page_num])  # Apply the rotation to the matrix
                
                # Convert the page to a Pixmap (image) using the transformation matrix
                pix = page.get_pixmap(matrix=matrix)  # Apply matrix to the page rendering
                
                # Define the output image path with page number
                output_image_path = f"{output_image_prefix}_page_{page_num + 1}.jpeg"
                image_paths.append(output_image_path)
                # Save the image to the specified file
                pix.save(output_image_path, jpg_quality=98)
                
                print(f"✅ Page {page_num + 1} converted to high-quality image and saved to {output_image_path}")
        
        except Exception as e:
            print(f"❌ An error occurred: {e}")

        return image_paths

    def images_to_pdf(self, image_paths, output_pdf_path):
        """
        Converts multiple images into a single PDF where each image represents a page.

        Args:
            image_paths (list): List of paths to the image files, each representing a page.
            output_pdf_path (str): Path where the output PDF will be saved.

        Returns:
            None
        """
        print(" ***** CONVERTING IMAGES TO PDF ***** ")
        try:
            # Create a new PDF document
            doc = fitz.open()
            
            # Loop through the list of image paths and add them to the PDF
            for image_path in image_paths:
                imgdoc=fitz.open(image_path)
                
                pdfbytes=imgdoc.convert_to_pdf()
                imgpdf=fitz.open("pdf", pdfbytes)
                doc.insert_pdf(imgpdf)
            
            # Save the document as a PDF
            doc.save(output_pdf_path)
            
            print(f"✅ Images converted to PDF and saved to {output_pdf_path}")
        
        except Exception as e:
            print(f"❌ An error occurred: {e}")
        
    def process_batch_pages(self):

        print(" ***** PROCESSING BATCH PAGES ***** ")

        try:
            s3_file = self.event['s3_file']
            pages_to_extract = self.event['pages_to_extract']
        except Exception:
            print("An error has ocurred, s3_file uri not found on event!")
            return
        
        print("FILE TO PROCESS", s3_file)
        print("PAGES TO EXTRACT", pages_to_extract)

        time_now = datetime.now().strftime('%Y%m%d%H%M%S%f')
        
        s3_uri = s3_file.split("//")[1]
        bucket = s3_uri.split("/")[0]
        file_name = s3_uri.split("/")[-1]
        extension = s3_file.split(".")[-1]

        path_to_save_output_files = "/".join(s3_uri.split("/")[1:-1])
        pdf_path = f"/tmp/temp_file_{time_now}.{extension}"
        output_image_path = f"/tmp/{file_name.replace(".pdf", "")}_{time_now}"  # Path to save the image/s
        output_pdf_path = f"/tmp/output_{file_name.replace(".pdf", "")}_{time_now}.pdf"

        if extension.lower() not in ["pdf"]:
            return "Extension not supported"
        
        print("***** DOWNLOADING FILE FROM S3 *****")
        download_file_from_s3(s3_file, pdf_path)

        image_paths = self.pdf_to_images(pdf_path, output_image_path, only_extract_pages=pages_to_extract)
        
        img_urls = []
        pdf_pages_urls = []

        for img_path in image_paths:
            image_name = img_path.split("/")[-1]
            
            image_url = post_to_s3(os.environ['BUCKET'], f'pdf_utils_processed/{file_name[:file_name.rfind(".")]}_{time_now}/images', image_name, img_path)
            img_urls.append(image_url)

        for img_path in image_paths:
            self.images_to_pdf([img_path], output_pdf_path)
            pdf_pages_urls.append(post_to_s3(os.environ['BUCKET'], f'pdf_utils_processed/{file_name[:file_name.rfind(".")]}_{time_now}/extracted', img_path.split("/")[-1].replace(".jpeg", ".pdf"), output_pdf_path))
        
        return {
            "splitted_images": img_urls,
            "splitted_pages": pdf_pages_urls
        }

    def process_document(self):
        
        print(" ***** PROCESSING DOCUMENT ***** ")

        try:
            s3_file = self.event['s3_file']
        except Exception:
            print("An error has ocurred, s3_file uri not found on event!")
            return
        
        print("FILE TO PROCESS", s3_file)
        time_now = datetime.now().strftime('%Y%m%d%H%M%S%f')

        s3_uri = s3_file.split("//")[1]
        bucket = s3_uri.split("/")[0]
        file_name = s3_uri.split("/")[-1]
        extension = s3_file.split(".")[-1]

        path_to_save_output_files = "/".join(s3_uri.split("/")[1:-1])
        pdf_path = f"/tmp/temp_file_{datetime.now().strftime('%Y%m%d%H%M%S')}.{extension}"
        output_image_path = f"/tmp/{file_name.replace(".pdf", "")}__{datetime.now().strftime('%Y%m%d%H%M%S')}"  # Path to save the image
        output_image_path_rotations = f"/tmp/{file_name.replace(".pdf", "")}_rotated_{datetime.now().strftime('%Y%m%d%H%M%S')}"  # Path to save the image

        if extension.lower() not in ["pdf"]:
            return "Extension not supported"
        
        print("***** DOWNLOADING FILE FROM S3 *****")
        download_file_from_s3(s3_file, pdf_path)

        image_paths = self.pdf_to_images(pdf_path, output_image_path)
        
        img_urls = []
        pdf_pages_urls = []
        rotations = []

        for idx, img_path in enumerate(image_paths):
            image_name = img_path.split("/")[-1]

            url_image = post_to_s3(os.environ['BUCKET'], f'pdf_utils_processed/{file_name[:file_name.rfind(".")]}_{time_now}/images', image_name, img_path)
            img_urls.append(url_image)
            rotations.append(0)


        image_paths_rotated = self.pdf_to_images(pdf_path, output_image_path_rotations, rotations)
        
        output_pdf_path = f"/tmp/multi_page_output_rotated_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"  # Output PDF path
        self.images_to_pdf(image_paths_rotated, output_pdf_path)
        file_url = post_to_s3(os.environ['BUCKET'], f"pdf_utils_processed/{file_name[:file_name.rfind(".")]}_{time_now}", file_name, output_pdf_path)

        for image_pth in image_paths_rotated:
            self.images_to_pdf([image_pth], output_pdf_path)
            pdf_pages_urls.append(post_to_s3(os.environ['BUCKET'], f'pdf_utils_processed/{file_name[:file_name.rfind(".")]}_{time_now}/pdfs', image_pth.split("/")[-1].replace(".jpeg", ".pdf"), output_pdf_path))
        
        return {
            "file_url": file_url,
            "splitted_images": img_urls,
            "splitted_pages": pdf_pages_urls
        }

    def concat_images(self):
        
        print(" ***** CONCATENATING IMAGES ***** ")

        try:
            s3_files = self.event['s3_files']
        except Exception:
            print("An error has ocurred, s3_files uri not found on event!")
            return
        
        print("IMAGES TO CONCAT", s3_files)

        time_now = datetime.now().strftime('%Y%m%d%H%M%S%f')

        local_img_paths = []
        for s3_file in s3_files:
            
            print("PROCESSING FILE", s3_file)
            
            s3_uri = s3_file.split("//")[1]
            file_name = s3_uri.split("/")[-1]
            extension = s3_file.split(".")[-1]

            if extension.lower() not in ["jpeg", "png"]:
                return "Extension not supported"
            
            img_local_path = f"/tmp/{file_name}_temp_file_{time_now}.{extension}"
            print(f"***** DOWNLOADING {file_name} FROM S3 *****")
            download_file_from_s3(s3_file, img_local_path)
            local_img_paths.append(img_local_path)
        
        output_pdf_path = f"/tmp/output_{file_name.replace(".pdf", "")}_{time_now}.pdf"
        self.images_to_pdf(local_img_paths, output_pdf_path)

        s3_uri = s3_files[0].split("//")[1]

        file_orig =  s3_uri.split("/")[2]
        file_name_output = f"output_concatenated_{file_orig}_{time_now}.pdf"
        print(f" ***** UPLOADING FILE TO S3 {file_name} *****")
        file_url = post_to_s3(os.environ['BUCKET'], f"pdf_utils_processed/{file_orig}_concat/concatenated", file_name_output, output_pdf_path)

        return {
            "file_url": file_url,
        }
    
    def process_text_to_pdf(self):
        
        print(" ***** PROCESSING TEXT TO PDF ***** ")
        
        time_now = datetime.now().strftime('%Y%m%d%H%M%S%f')
        file_name_output = f"text_to_pdf_processed_{time_now}.pdf"
        file_path = f"/tmp/{file_name_output}"

        try:
            text = self.event['text_to_process']
        except Exception:
            print("An error has occurred, 'text_to_process' not found in event!")
            return

        try:
            print(" ***** CREATING PDF FILE ***** ")
            doc = fitz.open()
            page = doc.new_page()
            font_size = 12
            margin = 50
            max_width = page.rect.width - 2 * margin
            y = 50

            # Load font for measuring
            font = fitz.Font("helv")

            def wrap_text(line):
                """Wrap a single line based on the width of the page."""
                words = line.split()
                wrapped_lines = []
                current_line = ""

                for word in words:
                    test_line = f"{current_line} {word}".strip()
                    test_width = font.text_length(test_line, fontsize=font_size)
                    if test_width <= max_width:
                        current_line = test_line
                    else:
                        wrapped_lines.append(current_line)
                        current_line = word
                if current_line:
                    wrapped_lines.append(current_line)
                return wrapped_lines

            for raw_line in text.split('\n'):
                for wrapped_line in wrap_text(raw_line):
                    page.insert_text((margin, y), wrapped_line, fontsize=font_size, fontname="helv")
                    y += font_size + 3
                    if y > page.rect.height - margin:
                        page = doc.new_page()
                        y = margin

            doc.save(file_path)
            doc.close()
        except Exception as e:
            print(f"Error creating PDF: {e}")
            return

        print(f" ***** UPLOADING FILE TO S3 {file_name_output} ***** ")
        file_url = post_to_s3(os.environ['BUCKET'], "pdf_utils_processed/text_to_pdf", file_name_output, file_path)

        return {
            "file_url": file_url,
        }
    
    def run(self):
        response = None
        if self.action == "process_document":
            response = self.process_document()
        elif self.action == "concat_images": 
            response = self.concat_images()
        elif self.action == "process_batch_pages_document": 
            response = self.process_batch_pages()
        elif self.action == "text_to_pdf": 
            response = self.process_text_to_pdf()
        return response

def lambda_handler(event, context):

    """
    Given the document as input, split the pdf in all the pages and imgs, and creates
    a new pdf with the generated imgs.
    {
        "action": "process_document",
        "s3_file" "s3://archivo.pdf"
    }

    Given an array of images, generates a new pdf with that images concatenated
    {
        "action": "concat_images",
        "s3_files" ["s3://img1.jpeg", "s3://img2.jpeg"]
    }

    Given a file and the pages to extract, returns the pdf and image of each page 
    {
        "action": "process_batch_pages_document",
        "s3_file" "s3://archivo.pdf",
        "pages_to_extract": [1, 2, 5]
    }

    Given a str, we create a pdf using that text 
    {
    "action":"text_to_pdf",
    "text_to_process":"[You don't often get <NAME_EMAIL>. Learn why this is important at https://aka.ms/LearnAboutSenderIdentification ]\nVendor Name:   Thomas T. Bratton\nBank Name:  Wells Fargo Bank N.A.\nRouting Number:  *********\nAccount Number:  *************\nThank you\nThomas Bratton\nSent from my iPhone"
    }
        
    """

    print(event)

    
    try:
        pdf_handler = PdfHandler(event)
        response = pdf_handler.run()
    except Exception as e:
        print("Operation couldnt be completed due to ", e)
        return {
            "statusCode": 500,
            "body": json.dumps({"message": f"Operation couldnt be completed due to: {e}"})
        }

    print(response)

    return {
        "statusCode": 200,
        "body": json.dumps(response)
    }


  
    
