from datetime import datetime, timed<PERSON>ta
from boto3_utilities import Boto3Utilities

class ProcessWrapper():
    def __init__(self):
        self.ssm_client = Boto3Utilities()

    def run_command_and_get_output(self, instance_id, commands):
        command_id = self.ssm_client.send_ssm_command(instance_id, commands)
        output = self.ssm_client.get_ssm_command_output(command_id, instance_id)
        print(output)
        return output

    def download_repo(self, gitlab_repo_url, gitlab_token, gitlab_branch, instance_id, working_dir):
        print("---------------Initiating repo download---------------")
        output = {'Repo download': ''}
        try:
            commands = [
                f"if (!(Test-Path '{working_dir}')) {{ New-Item -ItemType Directory -Path '{working_dir}' }}",
                f"Invoke-WebRequest -Uri 'https://gitlab.com/api/v4/projects/{gitlab_repo_url}/repository/archive.zip?sha={gitlab_branch}' -Headers @{{'Private-Token'='{gitlab_token}'}} -OutFile '{working_dir}\\repo.zip'",
                f"Expand-Archive -Path '{working_dir}\\repo.zip' -DestinationPath '{working_dir}\\temp' -Force",
                f"$rootFolder = Get-ChildItem -Path '{working_dir}\\temp' | Where-Object {{ $_.PSIsContainer }} | Select-Object -First 1",
                f"Move-Item -Path \"$($rootFolder.FullName)\\*\" -Destination '{working_dir}' -Force",
                f"Remove-Item -Recurse -Force '{working_dir}\\temp'",
                f"Remove-Item '{working_dir}\\repo.zip'"
            ]
            output['Repo download'] = self.run_command_and_get_output(instance_id, commands)
            return output
        except Exception as e:
            print(f'Error: {e}')
            output['Repo download'] = e
            return output

    def install_dependencies(self, instance_id, working_dir, python_path="python"):
        print("---------------Initiating installing dependencies---------------")
        output = {'Dependencies install': ''}
        try:
            requirements_path = f"{working_dir}\\requirements.txt"
            commands = [f"{python_path} -m pip install -r {requirements_path}"]
            output['Dependencies install'] = self.run_command_and_get_output(instance_id, commands)
            return output
        except Exception as e:
            print(f'Error: {e}')
            output['Repo download'] = e
            return output

    def launch_process(self, instance_id, working_dir, script_name, user, script_args='', python_path="python"):
        print("---------------Initiating launch process task---------------")
        output = {'Launch process': ''}
        try:
            script_path = f"{working_dir}\\{script_name}"
            batch_file_path = f"{working_dir}\\run_script.bat"
            start_time = (datetime.now() + timedelta(minutes=2)).strftime("%H:%M")
            task_name = f'RunPythonScript_{datetime.now().strftime("%H_%M_%S_%f")}'

            # Correctly create the batch file using PowerShell's Out-File
            create_batch_file_command = (
                f"Set-Content -Path '{batch_file_path}' -Value '@echo off`ncd /d \"{working_dir}\"`n{python_path} \"{script_name}\" {script_args}' -Encoding ASCII"
            )

            # Schedule the task to execute the batch file
            schedule_task_command = f"Schtasks /Create /F /SC ONCE /TN \"{task_name}\" /TR \"{batch_file_path}\" /RU \"{user}\" /ST {start_time} /IT"
            run_task_command = f"Schtasks /Run /TN \"{task_name}\""

            delete_task_command = f"Schtasks /Delete /TN \"{task_name}\" /F"
            commands = [
                f'powershell -Command "{create_batch_file_command}"',  # Ensure batch file exists
                schedule_task_command,  # Schedule the task
                run_task_command,       # Run the task immediately
                delete_task_command     # Delete the scheduled task to avoid duplicate execution
            ]

            output['Launch process'] = self.run_command_and_get_output(instance_id, commands)
            return output
        except Exception as e:
            print(f'Error: {e}')
            output['Launch process'] = str(e)
            return output

    def check_process(self, instance_id, task_name):
        print("---------------Initiating check process task---------------")
        output = {'Check process': ''}     
        try:
            commands = [
                f'schtasks /Query /TN "{task_name}" /FO LIST /V | findstr "Status"'
            ]
            output['Launch process'] = self.run_command_and_get_output(instance_id, commands)          
            return output
        except Exception as e:
            print(f'Error: {e}')
            output['Repo download'] = e
            return output    
    
    def cleanup(self, instance_id, working_dir, python_path="python"):
        print("---------------Initiating cleanup---------------")
        output = {'Cleanup': ''}         
        try:
            cleanup_folder_command = f"Remove-Item -Recurse -Force '{working_dir}'"
            cleanup_command = f"{python_path} -m pip freeze | ForEach-Object {{$_ -replace '==.*',''}} | ForEach-Object {{pip uninstall -y $_}}"
            delete_task_command = f"Schtasks /Delete /TN 'RunPythonScript' /F"
            commands = [cleanup_command, delete_task_command, cleanup_folder_command]
            output['Cleanup'] = self.run_command_and_get_output(instance_id, commands)           
            return output
        except Exception as e:
            print(f'Error: {e}')
            output['Repo download'] = e
            return output