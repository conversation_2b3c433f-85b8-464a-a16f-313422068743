from boto3_utils import get_secret
from pymongo import MongoClient
import os
from datetime import datetime

class MongoClientUtils:
    def __init__(self):
        mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json=False)
        self.client = MongoClient(mongo_uri)
        self.db = self.client[os.environ['MONGO_DATABASE']]


    def update_vin(self, collection, aria_wi_id, new_status, stage):

        filter = {f"flows.{stage}.docs.invoice.aria_data.aria_wi_id": aria_wi_id}
        to_update = f"flows.{stage}.docs.invoice"

        if "bol" in collection:
            filter = {f"flows.{stage}.docs.bol.aria_data.aria_wi_id": aria_wi_id}
            to_update = f"flows.{stage}.docs.bol"


        if "title" in collection:
            filter = {f"flows.{stage}.docs.title.aria_data.aria_wi_id": aria_wi_id}
            to_update = f"flows.{stage}.docs.title"


        self.collection = self.db['vin']

        self.collection.update_one(
                filter,
                {
                    "$set": {
                        f"{to_update}.aria_data.status": new_status,
                        f"{to_update}.updated_at": datetime.utcnow()
                    },
                    "$push": {
                        f"{to_update}.aria_data.status_history": new_status
                    }
                }
            )

    def update_wi_status(self, collection, aria_wi_id, new_status, stage):
        """
        Updates the folio_status and status_history of a folio in MongoDB.
        """

        self.collection = self.db[collection]

        try:
            print(f"Attempting to update status for work item ID {aria_wi_id} to '{new_status}'")
            result = self.collection.update_one(
                {"aria_wi_id": aria_wi_id},
                {
                    "$set": {
                        "status": new_status,
                        "updated_at": datetime.utcnow()
                    },
                    "$push": {
                        "status_history": {
                            "status": new_status
                        }
                    }
                }
            )

            self.update_vin(collection, aria_wi_id, new_status, stage)

            if result.matched_count > 0:
                print(f"Status updated for aria_wi_id {aria_wi_id}.")
            else:
                print(f"No found with aria_wi_id {aria_wi_id}.")
        except Exception as e:
            print(f"Failed to update status in MongoDB: {e}")
        
