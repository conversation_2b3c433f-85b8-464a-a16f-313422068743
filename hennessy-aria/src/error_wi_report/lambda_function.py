import os
import requests
import json
from datetime import datetime
from outlook_utils import Outlook
from aria_utils import AriaClient
from mongo_utils import MongoClientUtils
from boto3_utils import get_aria_credentials, get_parameter
import traceback

def lambda_handler(event, context):
    """
    Lambda function to query ARIA for work items and update their status.
    """

    print(event)

    stage = event.get("stage")

    if stage is None or stage == "":
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage provide!")
            }
        }

    aria_auth = get_aria_credentials()
    token = aria_auth.get('token')

    if not token:
        print("Error: Unable to retrieve the authentication token.")
        return {'statusCode': 500, 'body': "Error: Unable to retrieve the authentication token."}
    
    params = get_parameter(f"{os.environ['ENV']}-report_data_ids", return_json=True)
    apps = params[f"{os.environ['ENV']}-report_data_ids"][stage]
    
    
    processed_workitems = {}
    headers = {"Authorization": f"{token}", "Content-Type": "application/json"}
    
    mongo_client = MongoClientUtils()

    for app_name, app_data in apps.items():
        app_name = app_name.upper()
        
        if not all(app_data.values()):
            print(f"Skipping {app_name} due to missing configuration.")
            continue

        aria_client = AriaClient(app_id=app_data["app_id"], app_uuid=app_data["app_uuid"])
        workitems_url = f"{aria_client.aria_base_url}/work_items?filter[{app_data['status_column']}]={app_data['error_uuid']}"
        
        try:
            response = requests.get(workitems_url, headers=headers)
            print(f"{app_name} Response: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json().get("data", [])

                    workitem_ids = [item["id"] for item in data]
                    print(f"[{app_name}] Workitem IDs: {workitem_ids}")

                    processed_workitems[app_name] = []
                    for workitem_id in workitem_ids:
                        try:
                            aria_client.post_workitem_status_update(workitem_id, app_data["needs_uuid"])
                            mongo_client.update_wi_status(app_name.lower(), workitem_id, "Needs Attention", stage)
                            processed_workitems[app_name].append({"id": workitem_id, "status": "Needs Attention"})
                        except Exception as e:
                            print(f"[{app_name}] Error processing work item {workitem_id}: {e}")
                            
                except json.JSONDecodeError:
                    print(f"Error: JSON decode issue for {app_name}.")
                    print(traceback.format_exc())
            elif response.status_code == 204:
                print(f"No work items found for {app_name}.")
                print(traceback.format_exc())
                processed_workitems[app_name] = []
            else:
                print(f"Error fetching {app_name} work items: {response.status_code} - {response.text}")
                print(traceback.format_exc())

        
            print(processed_workitems)
        
        except requests.exceptions.RequestException as e:
            print(f"Request failed for {app_name}: {str(e)}")
            print(traceback.format_exc())
            processed_workitems[app_name] = []

    
    if any(processed_workitems.values()):
        try:
            env = os.environ.get('ENV')
            outlook = Outlook()
            support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
            copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
            sender = os.environ.get('REPORTER_EMAIL')
            
            subject = f"{env.upper()} - ARIA Work Item Error Report"
            body = outlook.generate_email_body_html(processed_workitems)
            
            outlook.send_email_notification(subject=subject, body=body, emails={
                "sender": sender,
                "recipients": support_emails,
                "bcc": copy_emails
            })
        except Exception as e:
            print(f"Error sending email: {e}")
            print(traceback.format_exc())
            return {'statusCode': 500, 'body': json.dumps({'message': f"Error sending email: {e}"})}
    
    return {'statusCode': 200, 'body': json.dumps({"Processed Workitems": processed_workitems})}

     