# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)

    def update_row_by_vin(self, vin, data_to_set, data_to_push):
        query = {"vin": vin}
        data_to_set['updated_at'] = datetime.datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.update_one(query, data)

    def update_row_with_bol_by_vin(self, vin, bol):

        update_data = {
            "$set": {
                "flows.post-inventory.docs.bol": {
                    "aria_data": {
                        "aria_wi_id": bol['aria_wi_id'],
                        "aria_app_id": bol['aria_app_id'],
                        "status": bol['status'],
                        "updated_at": bol['updated_at'],
                        "created": bol['read_at'],
                        "status_history": bol['status_history'],
                    },
                    "fields": bol['fields'],
                    "updated_at": datetime.datetime.now()
                },
                "updated_at": datetime.datetime.now()
            }
        }
        
        query = {"vin": vin}
        self.mongo.update_one(query, update_data)

    def update_row_with_title_by_vin(self, vin, title):

        update_data = {
            "$set": {
                "flows.post-inventory.docs.title": {
                    "aria_data": {
                        "aria_wi_id": title['aria_wi_id'],
                        "aria_app_id": title['aria_app_id'],
                        "status": title['status'],
                        "updated_at": title['updated_at'],
                        "created": title['read_at'],
                        "status_history": title['status_history'],
                    },
                    "fields": title['fields'],
                    "updated_at": datetime.datetime.now()
                },
                "updated_at": datetime.datetime.now()
            }
        }
        
        query = {"vin": vin}
        self.mongo.update_one(query, update_data)


    def __del__(self):
        self.mongo.close_connection()
