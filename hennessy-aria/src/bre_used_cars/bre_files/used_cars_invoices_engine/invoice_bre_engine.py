from bre_files.bre import Bre
from vininfo import Vin
import os
import pymongo
import re
class UsedCarsInvoicesBreEngine(Bre):

    def __init__(self, event, mongo_uri):
        super().__init__(event, mongo_uri)

        self.rules = {
            "required_fields_and_calculations": [800, 801, 802, 803, 804, 805, 809, 810, 811, 812, 813, 814, 815, 836, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 828, 829, 830, 831, 832, 834, 835, 838],
            "submit": [801, 802, 803, 804, 805, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 828, 829, 833, 834, 835, 838],
            "save": [],
            "review": [],
            "inspection": [],
            "manually": [],
            "restore_item": [801, 802, 803, 804, 805, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 828, 829, 833, 834, 835, 837, 838],
            "discard": []
        }

        
        self.bre_rules.update({
            800: { # Check first time duplicated
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [801],
            },
            801: {
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [],
            },
            802: {
                'tag': 'stock',
                'output_tags': ['stock'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            803: {
                'tag': 'make',
                'output_tags': ['make'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            804: {
                'tag': 'store',
                'output_tags': ['store'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            805: {
                'tag': 'invoice_date',
                'output_tags': ['invoice_date'],
                'pre_req': [],
            },
            806: {
                'tag': 'company_name',
                'output_tags': ['company_name'],
                'pre_req': [],
            },
            807: {
                'tag': 'address',
                'output_tags': ['address'],
                'pre_req': [],
            },
            808: {
                'tag': 'city',
                'output_tags': ['city'],
                'pre_req': [],
            },
            809: { # Calculate Apex amount
                'tag': 'apex_amount',
                'output_tags': ['apex_amount'],
                'pre_req': [812],
            },
            810: {
                'tag': 'transport_amount',
                'output_tags': ['transport_amount'],
                'pre_req': [836, 838] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [838],
            },
            811: {
                'tag': 'adjustments',
                'output_tags': ['adjustments'],
                'pre_req': [],
            },
            812: {
                'tag': 'purchase_price',
                'output_tags': ['purchase_price'],
                'pre_req': [],
            },
            813: {
                'tag': 'reynols_report',
                'output_tags': ['reynols_report'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            814: {
                'tag': 'transaction_location',
                'output_tags': ['transaction_location'],
                'pre_req': [],
            },
            815: { # Check VIN report is same as extracted
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [801, 800] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [801],
            },
            816: { #Fill VIN in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [815],
            },
            817: { #Fill STORE in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            818: { #Fill BRAND in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            819: { #Fill PREFIX in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [804],
            },
            820: { #Fill REFERENCE in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [836] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            821: { #Fill DATE in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [805],
            },
            822: { #Fill APEX AMOUNT in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [809] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [829],
            },
            823: { #Fill APEX ACCOUNTS in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [804],
            },
            824: { #Fill TRANSPORT AMOUNT in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [830] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [810],
            },
            825: { #Fill TRANSPORT ACCOUNT in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [804],
            },
            826: { #Fill INVENTORY in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [812],
            },
            827: { #Fill PAYOFF in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [],
            },
            828: { #Fill PAYABLE in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [812],
            },
            829: { #Check Apex amount is not empty
                'tag': 'apex_amount',
                'output_tags': ['apex_amount'],
                'pre_req': [809] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [],
            },
            830: { #Check if its a manheim invoice
                'tag': 'manheim_invoice',
                'output_tags': ['manheim_invoice'],
                'pre_req': [802],
            },
            831: { #Fill transport amount
                'tag': 'transport_amount',
                'output_tags': ['transport_amount'],
                'pre_req': [810],
            },
            832: { # Calculate DOWC amount
                'tag': 'dowc_amount',
                'output_tags': ['dowc_amount'],
                'pre_req': [],
            },
            833: { # Check non empty DOWC amount
                'tag': 'dowc_amount',
                'output_tags': ['dowc_amount'],
                'pre_req': [804],
            },
            834: { #Fill DOWC AMOUNT in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [832] if self.bre_type[self.document_type] == os.environ['DEFAULT_BRE_TYPE'] else [833],
            },
            835: { #Fill DOWC ACCOUNTS in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [804],
            },
            836: { # Fill report values
                'tag': 'reynols_report',
                'output_tags': ['reynols_report', 'store', 'make', 'stock', 'transport_amount'],
                'pre_req': [800],
            },
            837: { # Check duplicated
                'tag': 'vin',
                'output_tags': ['vin'],
                'pre_req': [],
            },
            838: { # Check transport amount has only numbers
                'tag': 'transport_amount',
                'output_tags': ['transport_amount'],
                'pre_req': [],
            },

        })

        self.valid_rules = self.rules[self.bre_type[self.document_type]]
        print("RULES TO EXECUTE", self.valid_rules)

        self.mapping_fields_row_uuid_stock_in_values = {}
        rows_stock_in = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        for k, v in rows_stock_in.items():
            field_name = v['cells']['field']['value']
            self.mapping_fields_row_uuid_stock_in_values[field_name] = k

        self.store = self.parsed_fields[self.document_type].get('store', {}).get('value')
        self.bre_matrix_used_cars = self.db.get_collection("bre_matrix_used_cars")
    
    def rule_800(self):
        """
        Rule 800: Validate VIN
        This rule checks the VIN (Vehicle Identification Number) for validity if exists in Aria.
        """
        input_data = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', '').get("value", "") != "" else ''
        vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")
        
        self.result['vin'] = {'pass': True, 'value': vin, 'display': True}

        attachment_id = str(self.parsed_fields[self.document_type]['attachment_id'].get('value'))
        invoice_s3_uri = str(self.parsed_fields[self.document_type]['invoice_s3_uri'].get('value'))

        self.invoice_collection = self.db.get_collection("invoice")

        invoice_vin_data = self.invoice_collection.find_one({"vin": vin})

        if invoice_vin_data is not None:
            self.result['vin']['pass'] = False
            self.result['vin']['message'] = 'VIN Duplicated'
            self.result['vin']['value'] = input_data
  
        data = {"$set": {"vin": vin}} 
        self.invoice_collection.update_one({"file_path": invoice_s3_uri, "attachment_id": attachment_id}, data)

    def rule_801(self):
        """
        Rule 801: Validate VIN
        This rule checks the VIN (Vehicle Identification Number) for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', '').get("value", "") != "" else ''
        vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")

        self.result['vin'] = {'pass': True, 'value': vin, 'display': True}
        if not input_data or input_data.replace(' ','') == '' or len(vin) != 17 or Vin(vin).verify_checksum() == False:
            self.result['vin']['pass'] = False
            self.result['vin']['message'] = 'VIN value failed verification. Wrong VIN or malformed.'
            self.result['vin']['value'] = input_data

    def rule_802(self):
        """
        Rule 802: Validate Store
        This rule checks the Store of the vehicle for validity.
        """
        value = self.parsed_fields[self.document_type].get('stock', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['stock'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(input_data, 'Stock')
    
    def rule_803(self):
        """
        Rule 803: Validate Brand
        This rule checks the Brand of the vehicle for validity.
        """
        value = self.parsed_fields[self.document_type].get('make', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['make'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(input_data, 'Make')
    
    def rule_804(self):
        """
        Rule 804: Validate Store
        This rule checks the Store of the vehicle for validity.
        """
        value = self.parsed_fields[self.document_type].get('store', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['store'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(input_data, 'Store')

    def rule_805(self):
        """
        Rule 805: Validate Invoice Date
        This rule checks the Invoice Date of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['invoice_date'].get('value')) if self.parsed_fields[self.document_type].get('invoice_date', {}).get("value", "") != "" else ''

        input_data = "" if input_data is None else input_data
        input_data = input_data.split(" ")[0]
        if "-" in input_data:
            vals = input_data.split("-")
            input_data = f"{vals[1]}/{vals[2]}/{vals[0]}"

        self.result['invoice_date'] = {'pass': True, 'value': input_data.replace(' ',''), 'message': "", 'display': True}

        input_data
        if not input_data or input_data.replace(' ','') == '' and not self.is_valid_date(input_data.replace(' ','')):
            self.result['invoice_date']['pass'] = False
            self.result['invoice_date']['message'] = 'Invoice date value not found'
            self.result['invoice_date']['value'] = input_data
            self.result['invoice_date']['coordinates'] = {}
        
    def rule_806(self):
        """
        Rule 806: Validate Company Name
        This rule checks the Company Name of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['company_name'].get('value')) if self.parsed_fields[self.document_type].get('company_name', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Company Name')
    
    def rule_807(self):
        """
        Rule 807: Validate Address
        This rule checks the Address of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['address'].get('value')) if self.parsed_fields[self.document_type].get('address', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Address')
    
    def rule_808(self):
        """
        Rule 808: Validate City
        This rule checks the City of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['city'].get('value')) if self.parsed_fields[self.document_type].get('city', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'City')
    
    def rule_809(self):
        """
        Rule 809: Validate Aex Amount
        This rule checks the Aex Amount of the vehicle for validity.
        """

        purchase_price_val = str(self.parsed_fields[self.document_type]['purchase_price'].get('value')) if self.parsed_fields[self.document_type].get('purchase_price', {}).get("value", "") != "" else ''
        purchase_price_val_clean = self.clean_amount_value(purchase_price_val)
        if purchase_price_val_clean != "":
            purchase_price_val_clean = float(purchase_price_val_clean)

        if not isinstance(purchase_price_val_clean, float):
            self.result['apex_amount'] = {'pass': False, 'value': "", 'message': "Couldnt calculate Apex amount due to error with Purchase price", 'display': True}
        else:
            if purchase_price_val_clean > 9999.99:
                apex_value = os.environ["APEX_AMT"]
                self.result['apex_amount'] = {'pass': True, 'value': apex_value, 'message': "", 'display': True}
            else:
                self.result['apex_amount'] = {'pass': True, 'value': 0, 'message': "", 'display': True}


    def rule_810(self):
        """
        Rule 810: Validate Transport Amount
        This rule checks the Transport Amount of the vehicle for validity.
        """
        value = self.parsed_fields[self.document_type].get('transport_amount', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['transport_amount'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(self.clean_amount_value(input_data), 'Transport amount')
    
    def rule_811(self):
        """
        Rule 811: Validate Adjustments
        This rule checks the Adjustments of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['adjustments'].get('value')) if self.parsed_fields[self.document_type].get('adjustments', {}).get("value", "") != "" else ''
        self.non_empty_rule(self.clean_amount_value(input_data), 'Adjustments')
    
    def rule_812(self):
        """
        Rule 812: Validate Purchase Price
        This rule checks the Purchase Price of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['purchase_price'].get('value')) if self.parsed_fields[self.document_type].get('purchase_price', {}).get("value", "") != "" else ''
        self.non_empty_rule(self.clean_amount_value(input_data), 'Purchase price')

    def rule_813(self):
        """
        Rule 813: Validate The Report Table
        This rule checks the The Report Table of the vehicle for validity.
        """
        input_data = self.parsed_fields[self.document_type]['reynols_report'].get('value', {}) or self.parsed_fields[self.document_type]['reynols_report'].get('rows', {})
        self.result['reynols_report'] = {'pass': True, 'value': input_data, 'message': "", 'display': True}

    def rule_814(self):
        """
        Rule 814: Validate Transaction location
        This rule checks the Transaction location of the vehicle for validity.
        """
        input_data = str(self.parsed_fields[self.document_type]['transaction_location'].get('value')) if self.parsed_fields[self.document_type].get('transaction_location', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Transaction location')

    def rule_815(self):
        extracted_vin_value = str(self.parsed_fields[self.document_type]['vin'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('vin', {}).get("value", "") != "" else ''

        report_vin_value = None
        rows_report = self.parsed_fields[self.document_type]['reynols_report'].get("rows", {})
        for k, v in rows_report.items():
            field_name = v['cells']['field']['value']
            if field_name == "VIN":
                report_vin_value = v['cells']['value']['value']

        self.result['invoice_date'] = {'pass': True, 'value': report_vin_value, 'message': "", 'display': True}
        if report_vin_value != extracted_vin_value:
            self.result['invoice_date'] = {'pass': False, 'value': report_vin_value, 'message': "VIN extracted and the VIN of the report are differents", 'display': True}

    def rule_816(self):
        field_name = "VIN"

        value = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]



        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'VIN value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'].update(rows)
    
    def rule_817(self):
        field_name = "STORE"

        value = str(self.parsed_fields[self.document_type]['store'].get('value')) if self.parsed_fields[self.document_type].get('store', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Store value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_818(self):
        field_name = "BRAND"

        value = str(self.parsed_fields[self.document_type]['make'].get('value')) if self.parsed_fields[self.document_type].get('make', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Brand value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_819(self):
        field_name = "PREFIX"

        value = self.bre_matrix_used_cars.find_one({"Store": self.store})["UC Prefix"]
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Prefix value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows

    
    def rule_820(self):
        field_name = "REFERENCE"

        value = str(self.parsed_fields[self.document_type]['stock'].get('value')) if self.parsed_fields[self.document_type].get('stock', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Reference value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows

    
    def rule_821(self):
        field_name = "DATE"

        value = str(self.parsed_fields[self.document_type]['invoice_date'].get('value')) if self.parsed_fields[self.document_type].get('invoice_date', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid] = {}
        rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Date value couldnt be filled','value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_822(self):
        field_name = "APEX AMOUNT"

        value = float(self.result['apex_amount'].get('value'))
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': -value if isinstance(value, float) else "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "" or isinstance(value, str):
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Apex amount DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Apex amount CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_823(self):
        field_name = "APEX ACCT"

        value_dt = self.bre_matrix_used_cars.find_one({"Store": self.store})["Debit_2"]
        value_cr = self.bre_matrix_used_cars.find_one({"Store": self.store})["Credit_2"]
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_dt, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_cr, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value_dt == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Apex account DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        if value_cr == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Apex account CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values']['value'] = rows
    
    def rule_824(self):
        field_name = "TRANSPORT AMOUNT"

        value = float(self.clean_amount_value(self.result['transport_amount'].get('value'))) if self.result.get('transport_amount', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': -value if isinstance(value, float) else "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "" or isinstance(value, str):
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Transport amount DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Transport amount CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_825(self):
        field_name = "TRANSPORT ACCT"

        value_dt = self.bre_matrix_used_cars.find_one({"Store": self.store})["Debit_1"]
        value_cr = self.bre_matrix_used_cars.find_one({"Store": self.store})["Credit_1"]

        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_dt, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_cr, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value_dt == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Transport account DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        if value_cr == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Transport account CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values']['value'] = rows
    
    def rule_826(self):
        field_name = "INVENTORY"
                
        value = float(self.clean_amount_value(self.parsed_fields[self.document_type]['purchase_price'].get('value'))) if self.parsed_fields[self.document_type].get('purchase_price', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid_dt] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Inventory amount value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values']['value'] = rows
    
    def rule_827(self):
        field_name = "PAYOFF"

        value = ""
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid_dt] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Payoff amount value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values']['value'] = rows
    
    def rule_828(self):
        field_name = "PAYABLE"

        value = float(self.clean_amount_value(self.parsed_fields[self.document_type]['purchase_price'].get('value'))) if self.parsed_fields[self.document_type].get('purchase_price', {}).get("value", "") != "" else ''
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[field_name]

        rows[row_uuid_dt] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'Payable amount value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        
        self.result['stock_in_values']['value'] = rows

    def rule_829(self):
        value = str(self.parsed_fields[self.document_type].get('apex_amount', {}).get("value", ""))
        input_data = str(self.parsed_fields[self.document_type]['apex_amount'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(self.clean_amount_value(input_data), 'Apex amount') 
    
    def rule_830(self):
        stock_number = str(self.parsed_fields[self.document_type]['stock'].get('value')) if self.parsed_fields[self.document_type].get('stock', {}).get("value", "") != "" else ''
        start_with_p = False
        if stock_number.replace(" ", "")[0].lower() == "p":
            start_with_p = True
        self.result["manheim_invoice"] = {"pass": True, 'value': start_with_p, 'display': True, 'confidence': 'None', 'coordinates': {}}

    def rule_831(self):
        transport_amount_value = ""
        rows_report = self.parsed_fields[self.document_type]['reynols_report'].get("rows", {})
        for k, v in rows_report.items():
            field_name = v['cells']['field']['value']
            if field_name == "MEMO 2":
                transport_amount_value = v['cells']['value']['value']

        self.result['transport_amount'] = {'pass': True, 'value': transport_amount_value, 'display': True, 'message': ''}
        if transport_amount_value == "" or transport_amount_value.replace(" ", "") == "":
            self.result['transport_amount']['pass'] = False
            self.result['transport_amount']['message'] = "Transport amount is empty"

    def rule_832(self):
        """
        Rule 809: Validate Aex Amount
        This rule checks the Aex Amount of the vehicle for validity.
        """
        dowc_val = os.environ["DOWC_AMT"]
        self.non_empty_rule(self.clean_amount_value(dowc_val), 'DOWC amount')

    def rule_833(self):
        """
        Rule 833: Validate DOWC
        This rule checks the DOWC of the vehicle for validity.
        """
        value = self.parsed_fields[self.document_type].get('dowc_amount', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['dowc_amount'].get('value')) if value != "" and str(value) != "None" else ''
        self.non_empty_rule(input_data, 'DOWC amount')

    def rule_834(self):
        field_name = "DOWC AMOUNT"
        value = float(self.result['dowc_amount'].get('value'))
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': -value if isinstance(value, float) else "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value == "" or isinstance(value, str):
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'DOWC DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'DOWC CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

            self.result['stock_in_values']['value'] = rows
    
    def rule_835(self):
        field_name = "DOWC ACCT"

        value_dt = self.bre_matrix_used_cars.find_one({"Store": self.store})["Debit_3"]
        value_cr = self.bre_matrix_used_cars.find_one({"Store": self.store})["Credit_3"]
        
        rows = self.parsed_fields[self.document_type]['stock_in_values'].get("rows", {})
        
        row_uuid_dt = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} DT"]
        row_uuid_cr = self.mapping_fields_row_uuid_stock_in_values[f"{field_name} CR"]

        rows[row_uuid_dt] = {}
        rows[row_uuid_cr] = {}
        rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_dt, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': True,'value': value_cr, 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values'] = {'pass': True, 'value': rows, 'display': True, 'message': ''}
        if value_dt == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_dt]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} DT", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'DOWC account DT value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}

        if value_cr == "":
            self.result['stock_in_values']['message'] = f'{field_name} calculation cant be done'

            rows[row_uuid_cr]['cells'] = {
            'field': {'pass': True,'value': f"{field_name} CR", 'display': True, 'confidence': 'None', 'coordinates': {}}, 
            'value': {'pass': False, 'message': 'DOWC account CR value couldnt be filled','value': "", 'display': True, 'confidence': 'None', 'coordinates': {}}}
        
        self.result['stock_in_values']['value'] = rows

    def rule_836(self):
        reynolds_report_rows = self.parsed_fields[self.document_type]['reynols_report'].get("rows", {})
        
        input_data = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', '').get("value", "") != "" else ''
        vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")
        self.vins_collection = self.db.get_collection("vin")
        vin_data = self.vins_collection.find_one({"vin": vin})

        if vin_data is not None:
            ## Fill all the reynolds report table in Aria
            for k, v in reynolds_report_rows.items():
                field_name = v['cells']['field']['value']

                if field_name == "Store":
                    value = vin_data['flows']["used-cars"]["report-data"]['store']
                elif field_name == "Received":
                    value = "" if str(vin_data['flows']["used-cars"]["report-data"]['received']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['received']
                elif field_name == "Stock":
                    value = vin_data['flows']["used-cars"]["report-data"]['stock']
                elif field_name == "VIN":
                    value = vin_data['vin']
                elif field_name == "Make":
                    value = vin_data['flows']["used-cars"]["report-data"]['make'].replace(" ", "")
                elif field_name == "Inv Amt":
                    value = "" if str(vin_data['flows']["used-cars"]["report-data"]['inv_amt']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['inv_amt']
                elif field_name == "SLS Cost":
                    value = "" if str(vin_data['flows']["used-cars"]["report-data"]['sls_cost']).lower() ==  "nan"  else vin_data['flows']["used-cars"]["report-data"]['sls_cost']
                elif field_name == "Stock In Notes":
                    value = "" if str(vin_data['flows']["used-cars"]["report-data"]['stock_in_notes']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['stock_in_notes']

                reynolds_report_rows[k]['cells'] = {
                    'field': {'pass': True,'value': field_name, 'display': True, 'confidence': 'None', 'coordinates': {}}, 
                    'value': {'pass': True,'value': value, 'display': True, 'confidence': 'None', 'coordinates': {}}}
                
            self.result['reynols_report'] = {'pass': True, 'value': reynolds_report_rows, 'display': True, 'message': ''}

            # Fill store
            self.result['store'] = {'pass': True, 'value': vin_data['flows']["used-cars"]["report-data"]['store'].replace(" ", ""), 'display': True}
            self.store = vin_data['flows']["used-cars"]["report-data"]['store'].replace(" ", "")

            # Fill stock
            self.result['stock'] = {'pass': True, 'value': vin_data['flows']["used-cars"]["report-data"]['stock'].replace(" ", ""), 'display': True}

            # Fill transport amount
            self.result['transport_amount'] = {'pass': True, 'value': str(vin_data['flows']["used-cars"]["report-data"]['memo']).replace(" ", ""), 'display': True}

            pattern = r"^(lexus|porsche|honda|ford|lincoln|cadillac|gm|mazda|jaguar|landrover)"
            text_to_get_make = vin_data['flows']["used-cars"]["report-data"]['make'].lower().replace(" ", "")
            match = re.match(pattern, text_to_get_make)
            make_val = vin_data['flows']["used-cars"]["report-data"]['make'].replace(" ", "")
            if match:
                make_val = match.group()
            # Fill make
            self.result['make'] = {'pass': True, 'value': make_val.upper(), 'display': True}
        
        else:
            self.result['reynols_report'] = {'pass': False, 'value': reynolds_report_rows, 'display': True, 'message': ''}
            self.result['store'] = {'pass': False, 'value': "", 'display': True}
            self.result['stock'] = {'pass': False, 'value': "", 'display': True}
            self.result['transport_amount'] = {'pass': False, 'value': "", 'display': True}
            self.result['make'] = {'pass': False, 'value': "", 'display': True}


    def rule_837(self):
        """
        Rule 800: Validate VIN
        This rule checks the VIN (Vehicle Identification Number) for validity if exists in Aria.
        """
        input_data = str(self.parsed_fields[self.document_type]['vin'].get('value')) if self.parsed_fields[self.document_type].get('vin', '').get("value", "") != "" else ''
        vin = input_data.upper().replace(" ", "").replace("O", "0").replace("I", "1").replace("Q", "9")
        
        self.result['vin'] = {'pass': True, 'value': vin, 'display': True}

        self.invoice_collection = self.db.get_collection("invoice")

        invoice_vin_data = self.invoice_collection.find_one({
            "$and": [
                { "vin": vin },
                { "status": { "$ne": "Discarded" } }
            ]
        })

        self.result['vin'] = {'pass': False, 'value': input_data, "message": "VIN Duplicated", 'display': True}

        if invoice_vin_data is None:
            self.result['vin']['pass'] = True
            self.result['vin']['message'] = ''
            self.result['vin']['value'] = input_data

    def rule_838(self):
        value = self.parsed_fields[self.document_type].get('transport_amount', {}).get("value", "")
        input_data = str(self.parsed_fields[self.document_type]['transport_amount'].get('value')) if value != "" and str(value) != "None" else ''
        has_chars = not all(char.isdigit() or char == '.' for char in input_data)

        self.result['transport_amount'] = {'pass': True, 'value': input_data, 'display': True}
        if has_chars:
            self.result['transport_amount']['pass'] = False
            self.result['transport_amount']['message'] = f'Transport amount contains chars. Value in report: \"{input_data}\"'
            self.result['transport_amount']['value'] = input_data

    def process_based_on_rules_bre(self):

        if self.valid_rules == self.passed_rules:
            aria_exception = ''
            next_status_id = self.action['target_status']
            note = ""
            
            next_status_label = self.status_info[next_status_id]['label']
            next_status = self.status_info[next_status_id]['key']

        else:
            
            next_status_id = self.action['source_status']

            actual_status = self.get_status_label_by_status_id(next_status_id, self.status_info).lower()
            next_status = "ready"
            aria_exception = ""
            note = ""

            print("Actual status" , actual_status)

            if "needs" in actual_status:
                if len(self.not_passed_rules) > 0:
                    
                    next_status = "needs"
                    aria_exception = "One or more fields require human intervention"
                    
                    if (len(self.not_passed_rules) == 1 and 810 in self.not_passed_rules):
                        next_status = "hold"
                        aria_exception = "Transport amount for this vehicle not found yet"

                    if (len(self.not_passed_rules) > 1 and (803 in self.not_passed_rules or 804 in self.not_passed_rules or 802 in self.not_passed_rules)):
                        next_status = "hold"
                        aria_exception = "Waiting for report data"

                    if 800 in self.not_passed_rules:
                        next_status = "discarded"
                        aria_exception = "VIN discarded as it is duplicated"
                           

            if "hold" in actual_status:
                if len(self.not_passed_rules) > 0:

                    next_status = "needs"
                    aria_exception = "One or more fields require human intervention"
                    
                    if len(self.not_passed_rules) == 1 and 810 in self.not_passed_rules:
                        next_status = "hold"
                        aria_exception = "Transport amount for this vehicle not found yet"
            
            if "discard" in actual_status:
                if len(self.not_passed_rules) > 0:

                    next_status = "needs"
                    aria_exception = ""
                    
                    if 837 in self.not_passed_rules:
                        next_status = "discard"
                        aria_exception = "VIN discarded as it is duplicated"

                    elif (len(self.not_passed_rules) == 1 and 810 in self.not_passed_rules):
                        next_status = "hold"
                        aria_exception = "Transport amount for this vehicle not found yet"

                    elif (len(self.not_passed_rules) > 1 and (803 in self.not_passed_rules or 804 in self.not_passed_rules or 802 in self.not_passed_rules)):
                        next_status = "hold"
                        aria_exception = "Waiting for report data"

            
                            
            next_status_id, next_status_label = self.get_status_label_info(next_status, self.status_info)

        if "ready" in next_status_label.lower():
            self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = True
            self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = True
        else:
            self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = False
            self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = False

        return next_status_id, next_status_label, aria_exception, note

    def process_default_bre(self):

        aria_exception = ""
        note = ""
        next_status = "ready"

        if len(self.not_passed_rules) > 0:

            next_status = "needs"
            aria_exception = "One or more fields require human intervention"
            
            if (len(self.not_passed_rules) == 1 and (810 in self.not_passed_rules or 836 in self.not_passed_rules)):
                next_status = "hold"

                if 810 in self.not_passed_rules:
                    aria_exception = "Transport amount for this vehicle not found yet"
                elif 836 in self.not_passed_rules:
                    aria_exception = "Waiting for report data"

            if len(self.not_passed_rules) == 1 and 800 in self.not_passed_rules:
                next_status = "discarded"
                aria_exception = "VIN discarded as it is duplicated"
       
        next_status_id, next_status_label = self.get_status_label_info(next_status, self.status_info)

        if "ready" in next_status_label.lower():
            self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = True
            self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = True
        else:
            self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = False
            self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = False

        return next_status_id, next_status_label, aria_exception, note