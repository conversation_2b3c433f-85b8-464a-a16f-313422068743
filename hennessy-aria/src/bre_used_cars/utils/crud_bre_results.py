# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret
import time

class CrudBreResults:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'bre_results'

    
    def create_bre_results_by_wi_id(self, data):
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.insert_one(data)
        
    def update_bre_results_by_wi_id(self, filter, data):
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(filter, data)

    def find_bre_results_by_wi_id(self, aria_wi_id):
        """
        This function finds an email by its email ID.
        """

        query = {"aria_wi_id": aria_wi_id}
        print(self.collection_name, type(self.collection_name))
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        response = self.mongo.find_one(query)

        return response


    def __del__(self):
        self.mongo.close_connection()
