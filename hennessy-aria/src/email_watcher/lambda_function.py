# This lambda function is responsible for fetching emails from a specified folder in Outlook, storing them in the MongoDB
# database, and initiating the processing of the email attachments

import io
import os
import csv
import json
import datetime
import traceback
from logger         import Logger
from outlook_utils  import Outlook
from crud_emails    import CrudEmails
from boto3_utils    import trigger_lambda

logger_class = Logger()
logger = logger_class.get_root_logger()

def return_error(status, message):
    logger.error(message)
    return {
        'statusCode': status,
        'body': json.dumps({'message': message})
    }

def write_emails_to_csv(emails):
    """
    This function writes the email documents to a CSV file.
    """
    csv_buffer = io.StringIO()

    writer = csv.DictWriter(csv_buffer, fieldnames=emails[0].keys())

    # Write the header
    writer.writeheader()

    # Write the email documents
    for email_document in emails:
        writer.writerow(email_document)

    # Get the CSV content
    csv_content = csv_buffer.getvalue()
    csv_buffer.close()

    return csv_content

def lambda_handler(event, context):
    """
    This method is the entry point for the AWS Lambda function. It fetches emails from the specified folder,
    extracts the email attachments and trigger the lambda that will create the folios. It will also insert the email
    and attachments details into the MongoDB database.
    Additionally, it will send an email notification to the specified email address with the details of the
    execution.
    """
    print(event)

    try:

        stage = event.get("stage", None)
        if stage is None or stage == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no stage provided!")
                }
            }
        

        filter_value = ""

        email_credentials_name = ""
        if stage == "post-inventory":
            email_credentials_name = "hennessy"
        elif stage == "used-cars":
            email_credentials_name = "hennessy_used_cars"
            sender_email = "<EMAIL>"

    
            # Construct the filter string
            today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            newer_date = today - datetime.timedelta(hours=24)
            iso_today = today.strftime('%Y-%m-%dT%H:%M:%SZ')
            iso_newer_date = newer_date.strftime('%Y-%m-%dT%H:%M:%SZ')
            
            #from/emailAddress/address eq '<EMAIL>' and receivedDateTime gt 2025-06-23T15:02:09Z and receivedDateTime lt 2025-06-24T15:02:09Z
            filter_value = f"from/emailAddress/address eq '{sender_email}' and receivedDateTime gt {iso_today} and receivedDateTime lt {iso_newer_date}"
            
            print("USING FILTER VALUE", filter_value)

        # Some parameters can be added as part of the request
        top_fetch = event.get('top_fetch', os.getenv('TOP_FETCH'))
        

        # Initialize classes
        env = os.environ['ENV']
        client_outlook = Outlook(f'{env}_{email_credentials_name}')
        crud_emails = CrudEmails()

        # Retrieve all emails from the specified folder
        source_folder = os.environ['SOURCE_FOLDER']
        try:
            logger.info(f"Retrieving last {top_fetch} emails")
            emails = client_outlook.get_emails_from_folder(source_folder, top_fetch, filter_value)
        except Exception as e:
            return return_error(500, f"Error fetching emails from folder {source_folder}: {str(e)}")

        # Process each email
        emails_processed = []

        for email in emails:
            email_id = email['internetMessageId']
            logger.info(f"Processing email with ID {email_id}")

            # Check if the email exists in the database
            email_exists = crud_emails.find_email_by_id(email_id)
            print(f"Trying processed email_exists with email_id {email_id}")
            if email_exists:
                continue

            error = None
            try:
                # Retrieve the email attachments
                attachments = client_outlook.get_email_attachments(email['internetMessageId'])
                print(f"new:attachment {attachments}")
            except Exception as e:
                error = f"Error fetching attachments: {str(e)}"
                logger.error(error)
                attachments = []

            # Insert the email into the database
            try:
                email_in_db = crud_emails.insert_email(email, attachments, source_folder)
            except Exception as e:
                # If email insertion fails, log the error and stop execution
                return return_error(500, f"Error inserting email into the database: {str(e)}. Stopping execution.")

            # If an error occurred while fetching the attachments, update the email status
            if error is not None:
                email_in_db = crud_emails.update_email_status(email['internetMessageId'], error=error)
                

            emails_processed.append(email_id)

        # Send logs to S3
        logger_class.send_log_to_s3(env + '-email_watcher')

        return {
            'statusCode': 200,
            'stage': stage,
            'body': {'emails_to_be_processed': emails_processed}
        }
    
    except Exception as e:
        print(f"Error when downloading invoices from s3: {traceback.format_exc()}")
        return {
            'statusCode': 200,
            'body': json.dumps({"message": f"Error when streaming bols on email account: {e}"})
        }
