# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudInvoices:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'invoice'
    
    def update_invoice_vin(self, vin, data):
        query = {"vin": vin}
        data['updated_at'] = datetime.datetime.now()
        data["status_history"] = ["Pending"]
        data = {"$set": data} 
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)


    def find_invoice_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)


    def __del__(self):
        self.mongo.close_connection()
