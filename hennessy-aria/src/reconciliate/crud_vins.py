# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudVins:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='vin'
    
    def update_vin(self, vin, data):
        query = {"vin": vin}
        data['last_updated_at'] = datetime.now()
        data = {"$set": data} 
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)

    def update_row_with_bol_by_vin(self, vin, bol):

        update_data = {
            "$set": {
                "flows.post-inventory.docs.bol": {
                    "aria_data": {
                        "aria_wi_id": bol['aria_wi_id'],
                        "aria_app_id": bol['aria_app_id'],
                        "status": bol['status'],
                        "updated_at": bol['updated_at'],
                        "created": bol['read_at'],
                        "status_history": bol['status_history'],
                    },
                    "fields": bol['fields'],
                    "updated_at": datetime.now()
                },
                "updated_at": datetime.now()
            }
        }
        
        query = {"vin": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, update_data)

    def update_row_with_title_by_vin(self, vin, title):

        update_data = {
            "$set": {
                "flows.post-inventory.docs.title": {
                    "aria_data": {
                        "aria_wi_id": title['aria_wi_id'],
                        "aria_app_id": title['aria_app_id'],
                        "status": title['status'],
                        "updated_at": title['updated_at'],
                        "created": title['read_at'],
                        "status_history": title['status_history'],
                    },
                    "fields": title['fields'],
                    "updated_at": datetime.now()
                },
                "updated_at": datetime.now()
            }
        }
        
        query = {"vin": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, update_data)


    def get_vin(self, vin):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"vin": vin}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def get_vins_without_bols_or_titles(self):
        query = {
            "$and": [
                {
                "$or": [
                    { "flows.post-inventory.docs.bol": {} },
                    { "flows.post-inventory.docs.title": {} }
                ]
                },
                {
                "flows.post-inventory.docs.invoice.aria_data.status": { 
                    "$in": ["Ready for DMS", "On Hold", "Completed", "Completed Manually"] 
                }
                }
            ]
        }
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find(query)
    

    def get_vins_with_selected_vin(self, vin):
        query = {"vin": vin}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find(query)
    
    def __del__(self):
        self.mongo.close_connection()