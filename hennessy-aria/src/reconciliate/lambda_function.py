import json
import os
from boto3_utils import get_secret
from mongo_utils import Mongo
from crud_vins import CrudVins
from crud_invoices import CrudInvoices
from crud_bols import CrudBols
from crud_titles import CrudTitles
from crud_statuses import CrudStatuses
import traceback
from typing import Dict, Any, Tu<PERSON>
from boto3_utils import trigger_lambda, get_presigned_url, trigger_lambda_response, generate_presigned_url

class Reconciliate():
    def __init__(self):
        self.mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)
        self.mongo_client = Mongo(self.mongo_uri)

        self.crud_vins = CrudVins(self.mongo_client)
        self.crud_invoices = CrudInvoices(self.mongo_client)  
        self.crud_bols = CrudBols(self.mongo_client)
        self.crud_titles = CrudTitles(self.mongo_client)
        self.crud_statuses = CrudStatuses(self.mongo_client)


    def get_status_label_info(self, status: str, status_info: Dict[str, Any]) -> Tuple[str, str]:
        for k, v in status_info.items():
            if status in v['label'].lower():
                return k, v['label']
        return '', ''


    def get_invoices_without_bol_or_title(self):
        """
        Get all the invoices that have not a BOL or a title
        """
        vins_list = self.crud_vins.get_vins_without_bols_or_titles()
        vins_to_be_processed = []

        for vin_data in vins_list:

            print("***** PROCESSING VIN *******")
            print(vin_data['vin'])
            print("***** ************** *******")

            vin = vin_data["vin"]
            something_changed = False

            document_id = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['aria_wi_id']
            app_id = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['aria_app_id']
            actual_status_label = vin_data["flows"]["post-inventory"]["docs"]['invoice']['aria_data']['status']
            
            
            status_dict = self.crud_statuses.get_statuses_by_app_id(app_id)['status']
            actual_aria_status_key, _ = self.get_status_label_info(actual_status_label.lower(), status_dict)

            if "completed" in actual_status_label.lower():
                target_aria_status_key = actual_aria_status_key
            else:
                target_aria_status_key, _ = self.get_status_label_info("ready", status_dict)

            fields_data = vin_data["flows"]["post-inventory"]["docs"]['invoice']['fields']
            
            if len(list(vin_data["flows"]["post-inventory"]["docs"].get("bol", {}).keys())) == 0:
                possible_bols = self.crud_bols.find_bol_using_vin(vin)
                print("Searching bols")
                for bol in possible_bols:
                    print("PROCESSING BOL -> ", bol)
                    if "complete" in bol.get("status", "").lower():
                        
                        self.crud_vins.update_row_with_bol_by_vin(
                            vin=vin, 
                            bol=bol
                        )

                        fields_data['bol_date']['value'] = bol['fields']['bol_date']['value']
                        presigned_url = get_presigned_url(os.environ['BUCKET'], f"{bol['path']}/{bol['attachment_name']}")
                        fields_data['bol_file']['value'] = presigned_url
                        print("PRESIGNED URL", presigned_url)

                        something_changed = True
                        break
            
            if len(list(vin_data["flows"]["post-inventory"]["docs"].get("title", {}).keys())) == 0:
                possible_titles = self.crud_titles.find_title_using_vin(vin)
                print(possible_titles)
                for title in possible_titles:
                    print("PROCESSING TITLE -> ", title)
                    if "complete" in title.get("status", "").lower():

                        try:

                            rows = title['fields']['vins']['value'] or title['fields']['vins']['rows']
                            pages = []
                            for k, v in rows.items():
                                if v['cells']['vin']['value'] == vin:
                                    pages = v['cells']['pages']['value'].split(",")
                                    break
                                        
                            pages = list(map(int, pages))
                            print("SEE THE PAGES SELECTED", pages)

                            title_path = f"s3://{os.environ['BUCKET']}/{title['path']}/{title['title_name']}"
                            
                            print(" ***** PROCESSING IMAGES ***** ")
                            print("title_path", title_path)
                            print({"action": "process_batch_pages_document", "s3_file": title_path, "pages_to_extract": pages})
                            pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "process_batch_pages_document", "s3_file": title_path, "pages_to_extract": pages})
                            if isinstance(pdf_processer_lambda_response, str):
                                pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

                            print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
                            print(pdf_processer_lambda_response)
                            print("********************************************")

                            if pdf_processer_lambda_response['statusCode'] != 200:
                                return {
                                    "statusCode": 500,
                                    "body": json.dumps({"message": "Error response from pdf_utils."})
                                }
                            
                            pdf_processer_lambda_response_body = json.loads(pdf_processer_lambda_response['body'])
                            title_pages_to_be_concatenated = pdf_processer_lambda_response_body['splitted_images']

                            print(" ***** CONCATENATING IMAGES ***** ")

                            pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "concat_images", "s3_files": title_pages_to_be_concatenated})
                            if isinstance(pdf_processer_lambda_response, str):
                                pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

                            print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
                            print(pdf_processer_lambda_response)
                            print("********************************************")

                            if pdf_processer_lambda_response['statusCode'] != 200:
                                return {
                                    "statusCode": 500,
                                    "body": {
                                        json.dumps({"message": "Error response from pdf_utils."})
                                    }
                                }
                            
                            pdf_processer_lambda_response_body = json.loads(pdf_processer_lambda_response['body'])

                            concated_title = pdf_processer_lambda_response_body['file_url']
                            
                            presigned_url = generate_presigned_url(concated_title)

                            print("PRESINGED URL FOR TITLE", presigned_url)

                            fields_data['title_file']['value'] = presigned_url

                            something_changed = True

                            self.crud_vins.update_row_with_title_by_vin(
                                vin=vin, 
                                title=title
                            )
                            
                            break

                        except Exception as e:
                            print(f"Error when linking the title to the invoice...Skipping...{traceback.format_exc()}")

            print("Have we change anything? ", something_changed)
            print("***** END PROCESSING VIN *******")
            
            if something_changed:
                
                vins_to_be_processed.append(vin)

                bre_event = {
                    "document": {
                        "id": document_id,
                        "app_id": app_id,
                        "aria_status": actual_aria_status_key,
                        "groups": {
                            "invoice": {"fields": fields_data}
                        },
                        "ocr_groups": ["invoice"]
                    },
                    "action": {
                        "source_status": actual_aria_status_key,
                        "target_status": target_aria_status_key,
                        "action_id": "000000000",
                        "action_label": "reprocess_completed" if "completed" in actual_status_label.lower() else "reprocess"
                    },
                    "status": status_dict
                }
                    

                # Trigger the BRE to reprocess the folio
                try:
                    trigger_lambda(os.getenv('BRE_HANDLER_LAMBDA'), {'body': json.dumps(bre_event)})
                except Exception as e:
                    return {
                        'statusCode': 500,
                        'body': json.dumps({'message': 'Failed to trigger BRE: {}'.format(str(e))})
                    }
                
        return {
            'statusCode': 200,
            'vins_to_be_processed': vins_to_be_processed
        }
                


    def run(self):
        
        response = self.get_invoices_without_bol_or_title()

        if response['statusCode'] == 200:
            if len(response['vins_to_be_processed']) > 0:
                trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": response['vins_to_be_processed'], "type": "reconciliate", "stage": "post-inventory"}})


def lambda_handler(event, context):

    print("Starting reconciliate lambda function")

    try:
        
        reconciliate = Reconciliate()
        reconciliate.run()

        return {
            'statusCode': 200,
            'body': json.dumps({'message': 'All vins processed corrrectly.'})
        }
    
    except Exception as e:
        print("Exception -> ", traceback.format_exc())
        return {
            'statusCode': 500,
            'body': json.dumps({'message': 'Issue while processing the petition: ' + str(e)})
        }
    