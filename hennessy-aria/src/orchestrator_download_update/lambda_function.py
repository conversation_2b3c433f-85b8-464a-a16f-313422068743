from smtp_utils         import SMTP
from crud_report_rows   import CrudReynolsReport
from boto3_utils import post_to_s3, trigger_lambda
from datetime           import datetime
import pandas           as pd
import traceback
import json
import os

def get_if_exists(obj, key, default=None):
    """
    Get the value of a key in a dictionary if it exists, otherwise return the default value.
    """
    key_parts = key.split('.')
    for part in key_parts:
        if obj is None:
            return default
        obj = obj.get(part, None)
    return obj

def generate_aria_report(stored_vins, failed_vins, vins_to_report):

    stored_vins_vin = [vin['vin'] for vin in stored_vins]
    print("stored_vins_vin", stored_vins_vin)

    failed_vins_vin = [vin['vin'] for vin in failed_vins]
    print("failed_vins_vin", failed_vins_vin)

    discarded_vins_vin = [vin['vin'] for vin in vins_to_report]
    print("discarded_vins_vin", discarded_vins_vin)

    all_vins = stored_vins_vin + failed_vins_vin + discarded_vins_vin

    trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": all_vins, "type": "download"}})

def lambda_handler(event, context):

    print(event)
    
    """
    Input:
    [
        [
            {
                "vin": "1HGCM82633A002352",
                "stored": True,
                "error": None
            },
            {
                "vin": "1HGCM82633A002353",
                "stored": False,
                "error": None
            }
        ],
        [
            {
                "vin": "1HGCM82633A002354",
                "stored": True,
                "error": None
            }
        ], 
        [
            {
                "vin": "1HGCM82633A002356",
                "stored": False,
                "error": "Failed to download"
            }
        ]
    ]

    This lambda function receives the output from the orchestrator in lotes of max 15 VINs per batch.
    It will update their status in the database and generate a report of every VIN that could not be
    downloaded in the last 3 days.
    """
    
    env = os.environ['ENV']
    smtp_client = SMTP()
    
    stage = None
    # Firstly, join all the VINs in a single list    
    vins_to_update = []
    for result_set in event:
        for result in result_set:
            # Parse the result body
            result_body = json.loads(result.get('result', {}).get('body', '{}'))
            # Retrieve the VINs and their status
            for vin, vin_data in result_body.items():
                vins_to_update.append({
                    "vin": vin,
                    "stored": vin_data.get("stored", False),
                    "error": vin_data.get("error", None),
                    "store": result.get('store', None)
                })
                
            stage = result.get('stage', None)
    
    if (stage is None or stage == "") and len(vins_to_update) != 0:
        raise ValueError("No stage provided in the event data")
    
    crud_report_rows = CrudReynolsReport(stage)

    
    print(" ****** UPDATING INVOICES STATUS ****** ")
    print(f"Found {len(vins_to_update)} VINs to update")

    # Failed vins
    failed_vins = list(filter(lambda vin: vin['stored'] == False, vins_to_update))
    # Split failed vins in group: Not found and Unexpected error
    not_found_vins = list(filter(lambda vin: vin['error'] == "VIN not found", failed_vins))
    unexpected_error_vins = list(filter(lambda vin: vin['error'] != "VIN not found", failed_vins))
    print(f"\t- {len(failed_vins)} VINs failed to download. From these, {len(unexpected_error_vins)} had an unexpected error")

    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in unexpected_error_vins]}},
        data={'$set': {'status': 2, 'updated_at': datetime.now()}, '$push': {'status_history': 2}}
    )

    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in not_found_vins]}},
        data={'$set': {'status': 1, 'updated_at': datetime.now()}, '$push': {'status_history': 1}}
    )

    # Stored vins
    stored_vins = list(filter(lambda vin: vin['stored'], vins_to_update))

    # Update the status of the stored vins
    crud_report_rows.update_many(
        filter={'vin': {'$in': [vin['vin'] for vin in stored_vins]}},
        data={'$set': {f'flows.{stage}.docs.invoice.downloaded_at': datetime.now(), 'status': 3}, '$push': {'status_history': 3}}
    )

    print(" DATABASE UPDATED SUCCESSFULLY ")

    # Client report - failed vins in the last 3 days
    vins_to_report = []
    if stage == 'post-inventory':
        vins_to_report = crud_report_rows.get_vins_to_report_post_inventory()
    elif stage == 'pre-inventory':
        pass
    elif stage == 'used-cars':
        vins_to_report = crud_report_rows.get_vins_to_report_used_cars()


    vins_to_report_formatted = [vin['vin'] for vin in vins_to_report]

    if vins_to_report_formatted:
        # Update the status of the vins to report
        crud_report_rows.update_many(
            filter={'vin': {'$in': [vins_to_report_formatted]}},
            data={'$set': {'status': 12}, '$push': {'status_history': 12}} # Discarded
        )

        trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": vins_to_report_formatted, "type": "discarded", "stage": stage}})

    else:
        print("No failed vins in the last 3 days")
    

    return {
        'statusCode': 200,
        # Return only vins of each list
        'body': json.dumps({
            'failed_vins': [vin['vin'] for vin in failed_vins],
            'vins_to_report': [vin['vin'] for vin in vins_to_report],
            'downloaded_vins': [vin['vin'] for vin in stored_vins]
        }, default=str)
    }