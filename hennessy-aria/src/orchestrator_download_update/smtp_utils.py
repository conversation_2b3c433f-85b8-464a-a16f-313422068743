import os
import smtplib
from email.mime.application         import MIMEApplication
from email.mime.multipart           import MIMEMultipart
from email.mime.text                import MIMEText
from datetime                       import datetime

from boto3_utils                    import get_secret

class SMTP:
    def __init__(self):
        """
        This method initializes the Outlook class with the client credentials.
        """
        credentials = get_secret(os.environ['ENV'] + '-email_credentials')
        self.config = credentials['aria_cloud']
        
        self.username = self.config['USERNAME_REPORTER']
        self.password = self.config['PASS_REPORTER_EMAIL']
        self.smtp = self.config['SMTP']
        self.port = self.config['PORT']


    def send_email_notification(self, subject, body, emails, csv_content=None, csv_name=None):
        message = MIMEMultipart()
        message['From'] = emails['sender']
        message['To'] = ", ".join(emails['recipients'])
        message['Subject'] = subject
        message.attach(MIMEText(body, 'html'))
        if csv_content:
            attachment = MIMEApplication(csv_content, Name=csv_name)
            attachment.add_header("Content-Disposition", f'attachment; filename={csv_name}')
            message.attach(attachment)

        try:
            server = smtplib.SMTP(self.smtp, self.port)
            server.starttls() 

            server.login(self.username, self.password)

            text = message.as_string()
            server.sendmail(emails['sender'], emails['recipients'] + emails['bcc'], text)
            print('Email sent successfully')
        except Exception as e:
            print(f"Error: {e}")
            
        finally:
            server.quit()

    def generate_email_body_html(self, processed_workitems, aria_url):
        """
        Generates an HTML email body with a table of stuck work items.
        """
        env = os.environ.get('ENV')
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        body = f"""
        <html>
        <body>
            <p>Hello,</p>
            <p>Please find below the detailed report of the work items currently stuck in processing in the ARIA system:</p>
            <hr>
            <h3>Work Items Stuck in Processing Summary</h3>
            <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
                <tr style="background-color: #f2f2f2;">
                    <th>WorkItem ID</th>
                    <th>Status</th>
                    <th>Link</th>
                </tr>
        """

        for item in processed_workitems:
            body += f"""
                <tr>
                    <td>{item['id']}</td>
                    <td><a href="{item['workitem_link']}" target="_blank">View Workitem</a></td>
                </tr>
            """

        body += f"""
            </table>
            <p><strong>Total Work Items Stuck in Processing:</strong> {len(processed_workitems)}</p>
            <p><strong>Link to app:</strong> <a href="{aria_url}">{aria_url}</a></p>

            <p><strong>Environment:</strong> {env}</p>
            <p><strong>Execution Timestamp:</strong> {timestamp}</p>
            <hr>
            <p>If you need further information or if there are any discrepancies, please refer to the system logs for more details or contact the support team.</p>
            <p>Thank you,<br>The ARIA System Team</p>
        </body>
        </html>
        """
        return body
