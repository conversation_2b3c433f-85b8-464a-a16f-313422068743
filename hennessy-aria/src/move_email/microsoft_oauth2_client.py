import json
import boto3
from botocore.exceptions import ClientError
import requests
import os

class MicrosoftOAuth2Client:
    """Class to handle Microsoft OAuth2 authentication using credentials from AWS Secrets Manager"""

    def __init__(self):
        self.secret_name = f"{os.environ['ENV']}-email_credentials"
        self.region_name = 'us-east-1'
        self.secret_key = f"{os.environ['ENV']}_hennessy"
        self.credentials = self.get_secret(self.secret_name, self.region_name)

        self.client_id = self.credentials[self.secret_key]['CLIENT_ID']
        self.client_secret = self.credentials[self.secret_key]['CLIENT_SECRET']
        self.tenant_id = self.credentials[self.secret_key]['TENANT_ID']
        self.user_id = self.credentials[self.secret_key]['USERID']
        self.access_token_url = f'https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token'
        self.scope = 'https://graph.microsoft.com/.default'  # Adjust scopes if needed

    def get_secret(self, secret_name, region_name):
        """Fetches secret from AWS Secrets Manager"""
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', region_name=region_name)

        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        except ClientError as e:
            print(f"Error fetching secret: {e}")
            raise e
        else:
            # Decrypts secret using the associated KMS key.
            secret = get_secret_value_response['SecretString']
            return json.loads(secret)

    def get_access_token(self):
        """Gets the access token using OAuth2 client credentials"""
        payload = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': self.scope,
            'grant_type': 'client_credentials'
        }

        # Make the request to get the access token
        response = requests.post(self.access_token_url, data=payload)

        if response.status_code == 200:
            token_data = response.json()
            return token_data['access_token']
        else:
            raise Exception(f"Error getting access token: {response.status_code} - {response.text}")
