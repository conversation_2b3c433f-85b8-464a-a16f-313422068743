import os
import json
from datetime import datetime
from sftp_utils import get_titles_file
import traceback
import base64
from aria_utils import AriaUtils, TemporalUnavailableException
from crud_titles import CrudTitles
import uuid
import fitz

def get_todays_title_s3_path():
    today = datetime.today()
    year = today.strftime("%Y") 
    month = today.strftime("%m") 
    day = today.strftime("%d") 
    
    #s3_path = f"{os.environ['COMPLETE_TITLE_FOLDER']}/{year}/{month}/{day}"
    s3_path = f"{os.environ['COMPLETE_TITLE_FOLDER']}"
    return s3_path


def count_pdf_pages(pdf_path):
    doc = fitz.open(pdf_path)
    return len(doc)

def lambda_handler(event, context):
        
    
    print(" ****** DOWNLOADING TITLE FILE ****** ")

    file_path_s3 = get_todays_title_s3_path()
    filenames = get_titles_file(os.environ['BUCKET'], file_path_s3)

    if filenames == [] or filenames is None:
        print("No new report found")
        return {
            "statusCode": 200,
            "body": json.dumps({"message": "No new report found"})
        }
    
    titles_processed = []
    print(filenames)
    for filename, time_uploaded in filenames:
        try:
            print(f" ****** PROCESSING FILE TITLE {filename} ****** ")

            file_path = "/tmp/" + filename

            with open(file_path, 'rb') as file:
                file_content = file.read()
            
            print(f" ****** SENDING TITLE TO ARIA {filename} ******  ")
            
            aria_utils = AriaUtils()
            crud_titles = CrudTitles()

            pdf_pages = count_pdf_pages(file_path)

            title_id = str(uuid.uuid4())

            wi_id = ""
            try:
                aria_utils.construct_create_request(base64.b64encode(file_content).decode('utf-8'), pdf_pages, title_id)
                work_item_data = aria_utils.send_post_request()
                wi_id = work_item_data['id']
            except TemporalUnavailableException as e:
                wi_id = ""
            except Exception as e:
                print(f"Error processing title: {traceback.format_exc()}")
                continue

            crud_titles.insert_title(title_id, time_uploaded, file_path_s3, filename , wi_id)
            titles_processed.append(filename)
            
        except Exception as e:
            print(f"Error processing title {filename}: {traceback.format_exc()}")

    
    return {
        "statusCode": 200,
        "response": "Titles processed correctly: " + json.dumps(titles_processed)
    }



    

    
    
    
