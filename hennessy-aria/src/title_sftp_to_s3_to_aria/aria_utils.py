import os
import time
import requests

from boto3_utils import get_secret

class TemporalUnavailableException(Exception):
    """ This exception will be handled in a different way than other exceptions. """
    def __init__(self, mensaje, response=None):
        super().__init__(mensaje)
        self.response = response

class AriaUtils:
    def __init__(self):
        self.aria_env = os.environ['ARIA_ENV']
        self.credentials = self.get_credentials()
        self.app_id = os.environ['ARIA_APP_ID']
        
        # Construct headers
        token = self.credentials['token']
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'{token}'
        }

    def get_credentials(self):
        """
        This function retrieves the credentials from the AWS Secrets Manager.
        """

        env = os.environ['ENV']
        credentials = get_secret(f"{env}-aria_cm_tokens")
        return credentials.get(self.aria_env)
    
    def construct_create_request(self, file_content, pdf_pages, unique_id):
        """
        This function constructs the request to send a workitem to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/document_processing"

        # Construct the payload
        group_name = os.environ['GROUP_NAME']
        self.payload = {
            "data": {
                "type": "CaseManagement",
                "attributes": {
                   "groups": [
                       {
                            "name": group_name,
                            "content": "data:application/pdf;base64," + file_content,
                            "metadata": [
                                {
                                    "name": "pdf_pages",
                                    "display": False,
                                    "value": pdf_pages
                                },
                                {
                                    "name": "unique_id",
                                    "display": False,
                                    "value": str(unique_id)
                                }
                            ]
                       }
                   ]
                }
            }
        }

    def construct_user_email_request(self, user_id):
        """
        This function constructs the request to retrieve the user's email from the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        self.url = f"{aria_url}/public/v1/users/user_email_by_id?user_id={user_id}"

    def construct_reply_bre_request(self, app_id, item_id, bre_response):
        """
        This function constructs the request to send a BRE response to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}/bre"

        # Construct the payload
        self.payload = {
			"data":{
				"type":"workItem",
				"id": item_id,
				"attributes":{
					"response": bre_response
				}
			}
		}
    
    def send_post_request(self):
        """
        This function sends a POST request to the ARIA CM.
        """

        # Send the request to the CM
        response = requests.post(self.url, headers=self.headers, json=self.payload)

        # Check status code - it should be 200 and include a link, otherwise raise an exception
        if response.status_code != 200:
            try:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.text}")
        
        response_url = response.json().get('links').get('self')
        aria_url = self.credentials['url']
        work_item_data = self.retrieve_connector_response(aria_url + response_url).get("data", {}) \
            .get("attributes", {}).get("document")
        return work_item_data
    
    def send_get_request(self):
        """
        This function sends a GET request to the ARIA CM.
        """

        # Send the request to the CM
        response = requests.get(self.url, headers=self.headers)

        # Check status code - it should be 200 and include a link, otherwise raise an exception
        if response.status_code not in [200, 202]:
            try:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            time.sleep(2)
            return self.retrieve_connector_response(self.url, try_count=1)

    def retrieve_connector_response(self, response_url, try_count=0):
        """
        This function retrieves the response from the connector. It will retry up to 2 times
        if the response is not ready.
        """
        if try_count > 10:
            raise TemporalUnavailableException("Failed to retrieve the connector response after 2 attempts")
        
        response = requests.get(response_url, headers=self.headers)
        if response.status_code == 202:
            try_count += 1
            time.sleep(2*try_count)
            return self.retrieve_connector_response(response_url, try_count)

        elif response.status_code == 200:
            return response.json()

        else:
            try:
                raise Exception(f"Failed to retrieve connector response: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to retrieve connector response: {response.status_code} - {response.text}")

