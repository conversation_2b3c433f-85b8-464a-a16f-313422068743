import json
from llm_utils import LLM


class LLM_Wrapper():
    def __init__(self, llm_params):
        self.llm_params = llm_params

    def call_llm(self, prompt, message):
        """
        This method will send a message to the pertinent llm
        """
        llm = LLM(self.llm_params['engine'], llm_params=self.llm_params)
        llm.generate_prompt(prompt=prompt)
        llm_output = llm.send_message_to_llm(message)
        llm_output = json.loads(llm_output)
        if llm_output.get('Error', None):
            if "maximum context length" in llm_output.get('Error'):
                return {}
            else:
                raise Exception(llm_output.get('Error', None))
        else: 
            return llm_output