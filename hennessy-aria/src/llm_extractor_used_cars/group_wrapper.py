import json
import requests
import ast
import traceback
import re
import os
from fields_to_extract import fields, fields_json, fields_type, fields_json_v2, fields_with_threshold
from prompts import extraction_prompt, date_transformer, multiroom_prompt, coord_prompt

class Group_Wrapper():
    def __init__(self, group_name):
        self.group_name = group_name
        self.ids_to_coord_mapping = {}
        self.ids_to_page_mapping = {}
        self.ocr_per_page_dict = {}
        self.ocr_per_page_plain_above_threshold = {}
        self.ocr_per_page_dict_above_threshold = {}
        self.ocr_per_page_dict_with_coords = {}        
        self.ocr_per_page_plain = {}
        self.total_pages = 0
        self.group_pages_list = []
        self.ocr_per_page_block = []
        self.ocr_per_line_per_page_plain = {}
        self.ocr_data = None
        self.extraction_prompt = extraction_prompt 



    def validate_extracted_data(self, main_data, validation_data1, validation_data2):
        """
        This method will apply the following validation matrix:

            GPT4 = A    | Claude = A    | Llama = A     --> Output = A
            GPT4 = A    | Claude = A    | Llama = B     --> Output = A
            GPT4 = A    | Claude = B    | Llama = C     --> Output = A
            GPT4 = A    | Claude = none | Llama = none  --> Output = A
            GPT4 = A    | Claude = B    | Llama = B     --> Output = repeat call to GPT
            GPT4 = none | Claude = A    | Llama = A     --> Output = repeat call to GPT
            GPT4 = none | Claude = none | Llama = none  --> Output = nope
        """
        fields_ok, fields_nok = [], []
        for k,v in main_data.items():
            # Cleaning values
            main_value = re.sub(" |[$]", "", str(v).lower())
            validation_value1 = re.sub(" |[$]", "", str(validation_data1.get(k, 'NONE')).lower())
            validation_value2 = re.sub(" |[$]", "", str(validation_data2.get(k, 'NONE')).lower())

            # print(main_value)s
            # print(validation_value1)
            # print(validation_value2)
            # If (A = B = C) or (B != C) or (B = C = none)
            if main_value == validation_value1 == validation_value2 or \
            validation_value1 != validation_value2 or \
            validation_value1 == validation_value2 == 'none':
                fields_ok.append(k)
            else:
                fields_nok.append(k)

        return fields_ok, fields_nok

    def extract_field_values_and_coordinates(self, gpt_output, id_regex, llm_wrapper_gpt):
        bre_fields_json = {}
        id_founds = []
        row_nr = 0
        for field, value in gpt_output.items():
            try:                
                original_field = field
                field = field.lower().replace(' ','_')
                
                # Regular field (plain text)
                if fields_type[self.group_name][original_field].get('type', None) == 'regular' and isinstance(gpt_output[original_field], str):
                    print('++++++++++++++++++++++++++++++++')
                    print(value)
                    print('++++++++++++++++++++++++++++++++')
                    if value.lower() == 'none':
                        continue
                    
                    id_founds = re.findall(id_regex, value)

                    if len(id_founds) == 0:
                        pass
                    # If a single word, use the coordinates of that word
                    elif len(id_founds) == 1:
                        coords = self.ids_to_coord_mapping[id_founds[0]]
                    # If multi-word value, we ask GPT to generate the surrounding box containing all the words
                    else:
                        c = 1
                        coords = ''
                        for id in id_founds:
                            coords+="""
                                Word{}:
                                x={}
                                y={}
                                width={}
                                height={}
                            
                            """.format(c, self.ids_to_coord_mapping[id]['Polygon'][0]['X'], self.ids_to_coord_mapping[id]['Polygon'][0]['Y'], self.ids_to_coord_mapping[id]['BoundingBox']['Width'], self.ids_to_coord_mapping[id]['BoundingBox']['Height'])
                            c+=1
                        coord_gpt_output = llm_wrapper_gpt.call_llm(prompt='You are a helpful AI assistant', message=coord_prompt.replace("{coords}", coords))
                    # Building JSON using Case Manager expected format
                    bre_fields_json[field] = {
                        "value": re.sub(id_regex, '', value), 
                        "coordinates": {
                            "x": coords['Polygon'][0]['X'] if len(id_founds) == 1 else coord_gpt_output['x'], 
                            "y": coords['Polygon'][0]['Y'] if len(id_founds) == 1 else coord_gpt_output['y'], 
                            "width": coords['BoundingBox']['Width'] if len(id_founds) == 1 else coord_gpt_output['width'], 
                            "height": coords['BoundingBox']['Height'] if len(id_founds) == 1 else coord_gpt_output['height'],
                            "page": self.ids_to_page_mapping[id_founds[0]] + 1
                        } if len(id_founds) > 0 else {},
                        "pass": True,
                        "display": True,
                        "message": ""
                    }
                
                #Line items (TO BE ENHACED)
                elif isinstance(gpt_output[original_field], dict):
                    bre_fields_json[field] = {"value": {}, "pass": True}
                    for f, v in gpt_output[original_field].items():
                        id_founds_f = re.findall(id_regex, str(f))
                        id_founds_v = re.findall(id_regex, str(v))
                        # Single word
                        if len(id_founds_f) == 1:
                            coords_f = self.ids_to_coord_mapping[id_founds_f[0]]
                            coords_v = self.ids_to_coord_mapping[id_founds_v[0]]
                        # Multi-word value
                        else:
                            coords_v = self.ids_to_coord_mapping[id_founds_v[0]]
                            c = 1
                            for id in id_founds_f:
                                coords+="""
                                    Word{}:
                                    x={}
                                    y={}
                                    width={}
                                    height={}
                                
                                """.format(c, self.ids_to_coord_mapping[id]['Polygon'][0]['X'], self.ids_to_coord_mapping[id]['Polygon'][0]['Y'], self.ids_to_coord_mapping[id]['BoundingBox']['Width'], self.ids_to_coord_mapping[id]['BoundingBox']['Height'])
                                c+=1
                            coord_gpt_output = llm_wrapper_gpt.call_llm(prompt='You are a helpful AI assistant', message=coord_prompt.replace("{coords}", coords))                                                       
                        bre_fields_json[field]['value'][str(row_nr)] = {
                            "cells": {
                                fields_type[self.group_name][original_field]['columns'][0]: {
                                    "value": re.sub(id_regex, '', f),
                                    "coordinates": {
                                        "x": coords_f['Polygon'][0]['X'] if len(id_founds_f) == 1 else coord_gpt_output['x'], 
                                        "y": coords_f['Polygon'][0]['Y'] if len(id_founds_f) == 1 else coord_gpt_output['y'], 
                                        "width": coords_f['BoundingBox']['Width'] if len(id_founds_f) == 1 else coord_gpt_output['width'], 
                                        "height": coords_f['BoundingBox']['Height'] if len(id_founds_f) == 1 else coord_gpt_output['height'],
                                        "page": self.ids_to_page_mapping[id_founds_f[0]] + 1
                                    },
                                    "pass": True,
                                    "display": True
                                },
                                fields_type[self.group_name][original_field]['columns'][1]: {
                                    "value": re.sub(id_regex, '', v),
                                    "coordinates": {
                                        "x": coords_v['Polygon'][0]['X'], 
                                        "y": coords_v['Polygon'][0]['Y'], 
                                        "width": coords_v['BoundingBox']['Width'], 
                                        "height": coords_v['BoundingBox']['Height'],
                                        "page": self.ids_to_page_mapping[id_founds_v[0]] + 1
                                    },
                                    "pass": True,
                                    "display": True
                                }
                            }
                        }
                        row_nr+=1
                else:
                    print('Unexpected field type extracted by LLM ({} vs {})'.format(type(gpt_output[original_field]), fields_type[self.group_name][original_field].get('type', None)))
            except Exception as e:
                print(f"ERROR: {traceback.format_exc()}")
                continue
        
        return bre_fields_json
    
    def generate_ocr_data_by_threshold(self, threshold_up=None, threshold_down=None, threshold_left=None, threshold_right=None):
        """
        Processes OCR data and filters words based on optional geometric thresholds.

        :param threshold_up: Minimum Y coordinate (top bound)
        :param threshold_down: Maximum Y coordinate (bottom bound)
        :param threshold_left: Minimum X coordinate (left bound)
        :param threshold_right: Maximum X coordinate (right bound)
        """
        ocr_data_pages = self.ocr_data

        for block in ocr_data_pages['Blocks']:
            if block['BlockType'] != 'WORD':
                continue

            page_nr = int(block['Page']) - 1
            geometry = block['Geometry']
            polygon = geometry['Polygon']
            min_x = min(p['X'] for p in polygon)
            max_x = max(p['X'] for p in polygon)
            min_y = min(p['Y'] for p in polygon)
            max_y = max(p['Y'] for p in polygon)

            text = block['Text']

            # Check if block meets threshold conditions
            within_threshold = True
            if threshold_up is not None and max_y < threshold_up:
                within_threshold = False
            if threshold_down is not None and min_y > threshold_down:
                within_threshold = False
            if threshold_left is not None and max_x < threshold_left:
                within_threshold = False
            if threshold_right is not None and min_x > threshold_right:
                within_threshold = False

            if within_threshold:
                self.ocr_per_page_dict_above_threshold[page_nr].append({'Text': text, 'Id': self.mapping_ids_aws_to_word_id[block['Id']]})
                self.ocr_per_page_plain_above_threshold[page_nr] += text + ' '
        
    def download_ocr_data(self, link_to_download):
        """
        This method downloads the json file containing the Textract OCR results (generated by ARIA connector) and generates the needed ocr variables (per page, etc)
        """
        r = requests.get(link_to_download)
        #ocr_data = ast.literal_eval(r.content.decode('utf-8'))    
        ocr_data = json.loads(r.content.decode('utf-8'))
        self.ocr_data = ocr_data
        self.ids_to_coord_mapping = {}
        self.ids_to_page_mapping = {}
        self.ocr_per_page_dict = {}
        self.ocr_per_page_dict_with_coords = {}        
        self.ocr_per_page_plain = {}
        self.ocr_per_line_per_page_plain = {}
        self.mapping_ids_aws_to_word_id = {}
        page_nr = 1
        self.total_pages = ocr_data['DocumentMetadata']['Pages']
        word_id = 1
        
        try:
            # ANALYSIS
            ocr_data_pages = ocr_data['ExpenseDocuments']
            for page in ocr_data_pages:
                self.ocr_per_page_dict[page_nr] = []
                self.ocr_per_page_dict_with_coords[page_nr] = []
                self.ocr_per_page_plain[page_nr] = ''
                self.ocr_per_page_plain_above_threshold[i] = ''
                self.ocr_per_page_dict_above_threshold[i] = []
                for block in page['Blocks']:
                    if block['BlockType'] == 'WORD':
                        self.ids_to_coord_mapping[str(word_id)] = block['Geometry']
                        self.ids_to_page_mapping[str(word_id)] = page_nr
                        self.ocr_per_page_dict[page_nr].append({'Text': block['Text'], 'Id': str(word_id)})
                        self.ocr_per_page_block.append({'Text': block['Text'], 'Id': str(word_id), 'Page': block['Page']})
                        self.ocr_per_page_plain[page_nr]+=block['Text']+' '
                        self.mapping_ids_aws_to_word_id[block['Id']] = word_id
                        self.ocr_per_page_dict_with_coords[page_nr].append({'Text': block['Text'], 'Id': str(word_id), 'Coords': str(block['Geometry']['Polygon'])})
                        word_id+=1
                page_nr+=1
        except:
            # NOT ANALYSIS
            ocr_data_pages = ocr_data
            for i in range(self.total_pages):
                self.ocr_per_page_dict[i] = []
                self.ocr_per_page_dict_with_coords[i] = []
                self.ocr_per_page_plain[i] = ''
                self.ocr_per_line_per_page_plain[i] = ''
                self.ocr_per_page_plain_above_threshold[i] = ''
                self.ocr_per_page_dict_above_threshold[i] = []
            for block in ocr_data_pages['Blocks']:
                if block['BlockType'] == 'WORD':
                    page_nr = int(block['Page']) - 1
                    self.ids_to_coord_mapping[str(word_id)] = block['Geometry']
                    self.ids_to_page_mapping[str(word_id)] = page_nr
                    self.ocr_per_page_dict[page_nr].append({'Text': block['Text'], 'Id': str(word_id)})
                    self.ocr_per_page_block.append({'Text': block['Text'], 'Id': str(word_id), 'Page': block['Page']})
                    self.ocr_per_page_plain[page_nr]+=block['Text']+' '
                    self.ocr_per_page_dict_with_coords[page_nr].append({'Text': block['Text'], 'Id': str(word_id), 'Coords': str(block['Geometry']['Polygon'])})
                    self.mapping_ids_aws_to_word_id[block['Id']] = word_id
                    word_id+=1
                elif block['BlockType'] == 'LINE':
                    page_nr = int(block['Page']) - 1
                    self.ocr_per_line_per_page_plain[page_nr]+=block['Text']+'\n'



    def classify_pages(self, mongo_client, llm_wrapper, execution_id, classify_needed):
        """
        This method will generate a json with the classified pages in this format:
            {'room_number_1': 'A-B', 'room_number_2': 'C-D'}
        The output will be in this format:
            [[A, A+1, A+2, A+3, ...B], [C, C+1, C+2, C+3, ...D]]
        """
        if not classify_needed:
            self.group_pages_list = [[x for x in range(self.total_pages)]]
        else:
            print("TOTAL PAGES ", self.total_pages)
            self.group_pages_list = [list(range(i, min(i + 10, self.total_pages))) for i in range(0, self.total_pages, 10)]
            print("BATCHES ", self.group_pages_list)

    def generate_table(self, counter, bre_fields_json, table_fields):
        """
        This method generates a table using isolated fields
        """        
        cells = {}
        for item in table_fields:
            cells[item] = bre_fields_json[item] if bre_fields_json.get(item) else {"value":" ", "pass":True, "display":True, "message":""}
        
        return {
            "value":{
                str(counter):{
                "cells": cells
                }
            }
        }    

    def generate_table_by_data_string(self, data_string, table_fields, existing_rows_ids=[]):
        
        if data_string == "":
            return {
                "value": {},
                "display": True,
                "pass": True
            } 

        print(data_string)
        data = data_string.split(",")

        id_regex = "\[Id: '([0-9]*)']"

        rows = {}

        for idx, data in enumerate(data):
            cells = {}
            for item in table_fields:
            
                data_value = data.split("[")[0][:-1]

                cells[item] = {
                    "value": data_value.replace(" ", ""), 'pass': True, 'display': True, 'message': ''
                }

                id_val = re.search(id_regex, data)
                if id_val:
                    match_id_val = id_val.group(1)

                if match_id_val:
                    cells[item].update({"coordinates": {
                                    "x": self.ids_to_coord_mapping[match_id_val]['Polygon'][0]['X'],
                                    "y": self.ids_to_coord_mapping[match_id_val]['Polygon'][0]['Y'],
                                    "width": self.ids_to_coord_mapping[match_id_val]['BoundingBox']['Width'], 
                                    "height": self.ids_to_coord_mapping[match_id_val]['BoundingBox']['Height'],
                                    "page": self.ids_to_page_mapping[match_id_val] + 1
                                }})


            rows[str(idx)] = {}
            rows[str(idx)]['cells'] = cells
                            
        return {
            "value": rows,
            "display": True,
            "pass": True
        }    


    def process_group(self, llm_wrapper_gpt, llm_wrapper_gpt_mini, llm_wrapper_claude, llm_wrapper_llama, mongo_client, execution_id):
        print('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
        print(self.ocr_per_page_dict)
        print(self.ocr_per_page_dict_with_coords)
        print(self.ocr_per_page_plain)
        print('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
        llm_call_error = True        
        global_bre_fields_json = {}
        page_block_nr = 0
        for page_range in self.group_pages_list:
            page_block_nr+=1
            raw_text_ocr = []
            raw_text_ocr_with_coords = []
            raw_text_plain = ''
            raw_text_ocr_above_threshold = []
            raw_text_plain_above_threshold = ''

            # Iterating only up to total_pages or the last page in the range
            actual_page_range = [page for page in page_range if page <= (self.total_pages - 1)]
            for page in actual_page_range:
                raw_text_ocr.append(self.ocr_per_page_dict[page])
                raw_text_ocr_with_coords.append(self.ocr_per_page_dict_with_coords[page])
                raw_text_plain+=self.ocr_per_page_plain[page]

            to_extract = fields[self.group_name]
            
            # Calling GPT to extract data
            message=f"""Extract the following: \n{to_extract} \n\n from the following text: \n{raw_text_ocr}\n\nThis is the previous text split by lines:\n"""
            message_without_ids = f"""Extract the following: \n{to_extract} \n\n from the following text: \n{raw_text_plain}\n\nThis is the previous text split by lines:\n"""
            for page in page_range:
                message += "\n".join(self.ocr_per_line_per_page_plain[page].split("\n")) + "\n"
                message_without_ids += "\n".join(self.ocr_per_line_per_page_plain[page].split("\n")) + "\n"

            gpt_output = llm_wrapper_gpt.call_llm(prompt=self.extraction_prompt.replace("{json}", json.dumps(fields_json[self.group_name])), message=message)

            mongo_client.select_db_and_collection(db_name=os.environ.get('MONGO_DATABASE'), collection_name=os.environ["LLM_EXTRACTOR_COLLECTION_NAME"])
            mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {f"{self.group_name}.extracted_data_gpt4": gpt_output}})



            # Calling auxiliar LLMs (optional)
            retry_calling = 1
            while(llm_call_error == True and retry_calling > 0):
                try:

                    # Calling Claude3 Haiku to extract data
                    claude_output = llm_wrapper_claude.call_llm(prompt=self.extraction_prompt.replace("{json}", json.dumps(fields_json_v2[self.group_name])), message=message_without_ids)
                    mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {f"{self.group_name}.extracted_data_claude3": claude_output}})

                    # Calling Llama3 8b to extract data
                    llama3_output = llm_wrapper_llama.call_llm(prompt=self.extraction_prompt.replace("{json}", json.dumps(fields_json_v2[self.group_name])), message=message_without_ids)
                    mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {f"{self.group_name}.extracted_data_llama3": llama3_output}})

                    llm_call_error = False

                except Exception as e:
                    #print('Issue while calling other llms: {}'.format(traceback.format_exc()))
                    llm_call_error = True
                    retry_calling -= 1

            if fields_with_threshold.get(self.group_name) is not None:
                threshold_prompt = {}
                threshold_message = {}
                threshold_prompt_without_ids = {}
                print("Fields with thresholds")
                print(fields_with_threshold[self.group_name])
                for field, thresholds in fields_with_threshold[self.group_name].items():
                    threshold_prompt = {}
                    threshold_message = {}
                    threshold_prompt[field] = fields_json[self.group_name][field]
                    threshold_prompt_without_ids[field] = fields_json_v2[self.group_name][field]
                    threshold_message[field] = fields[self.group_name][field]
                
                    self.generate_ocr_data_by_threshold(thresholds.get("threshold_up", None), thresholds.get("threshold_down", None), thresholds.get("threshold_left", None), thresholds.get("threshold_right", None))
                    for page in actual_page_range:
                        raw_text_ocr_above_threshold.append(self.ocr_per_page_dict_above_threshold[page])
                        raw_text_plain_above_threshold+=self.ocr_per_page_plain_above_threshold[page]

                    gpt_output3 = llm_wrapper_gpt.call_llm(prompt=extraction_prompt.replace("{json}", json.dumps(threshold_prompt)), message=f"""Extract the following: \n{threshold_message} \n\n from the following text: \n{raw_text_ocr_above_threshold}""")
                    
                    claude_output3 = llm_wrapper_claude.call_llm(prompt=extraction_prompt.replace("{json}", json.dumps(threshold_prompt_without_ids)), message=f"""Extract the following: \n{threshold_message} \n\n from the following text: \n{raw_text_plain_above_threshold}""")
                    llama3_output3 = llm_wrapper_llama.call_llm(prompt=extraction_prompt.replace("{json}", json.dumps(threshold_prompt_without_ids)), message=f"""Extract the following: \n{threshold_message} \n\n from the following text: \n{raw_text_plain_above_threshold}""")

                    gpt_output[field] = gpt_output3[field]
                    claude_output[field] = claude_output3[field]
                    llama3_output3[field] = llama3_output3[field]

            # Validating extracted data (optional)
            id_regex = "\[Id: '([0-9a-z-]*)']"
            if not llm_call_error:
                # Cleaning GPT json
                gpt_output_clean = {}
                for k,v in gpt_output.items():
                    if isinstance(v, dict): 
                        gpt_output_clean[k] = json.loads(re.sub(id_regex, '', json.dumps(v))) 
                    else:
                        gpt_output_clean[k] = re.sub(id_regex, '', v)
                
                print(gpt_output_clean, claude_output, llama3_output)
                fields_ok, fields_nok = self.validate_extracted_data(gpt_output_clean, claude_output, llama3_output)
                print('>>>>>>>>>>>>>>>>>>>>>>>>>>>')
                print(fields_ok)
                print('>>>>>>>>>>>>>>>>>>>>>>>>>>>')
                print(fields_nok)
                print('>>>>>>>>>>>>>>>>>>>>>>>>>>>')

                mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {f"{self.group_name}.fields_ok": fields_ok, f"{self.group_name}.fields_nok": fields_nok}})

                # If fields_nok, call again GPT (TO BE ENHACED)
                if fields_nok and len(fields_nok) > 0:

                    gpt_output2 = llm_wrapper_gpt.call_llm(prompt=self.extraction_prompt.replace("{json}", json.dumps(fields_json[self.group_name])), message=f"""Extract the following: \n{to_extract} \n\n from the following text: \n{raw_text_ocr}\n\n\n\nUse these values extracted by other LLM as reference:\n{claude_output}""")

                    for field in fields_nok:
                        gpt_output[field] = gpt_output2[field]
                    print("---------------Final Extracted data ---------------")
                    print(gpt_output)
                mongo_client.update_one(filter={'execution_id': execution_id}, data={"$set": {f"{self.group_name}.extracted_data_gpt4_final": gpt_output}})




            # Iterating each extracted field to extract the value and coordinates
            bre_fields_json = self.extract_field_values_and_coordinates(gpt_output, id_regex, llm_wrapper_gpt_mini)

            # Dates operations (optional)   
            try:
                # Extracting the fields with dates
                date_fields_list = [x.lower().replace(' ','_') for x in fields[self.group_name] if 'date' in x]
                if date_fields_list:
                    date_fields_json = {}
                    for field in date_fields_list:
                        if bre_fields_json.get(field, None):
                            date_fields_json[field] = bre_fields_json.get(field)
                    # Transforming dates into american format
                    date_fields_json = llm_wrapper_gpt.call_llm(prompt=date_transformer, message=f"""Here you have the json: \n{date_fields_json} \n\n Just provide the transformed json as output.""")            
                    # Merging the transformed dates into original json
                    for field in date_fields_list:
                        if date_fields_json.get(field, None):
                            bre_fields_json[field] = date_fields_json.get(field)
            except Exception as e:
                print('Issue while operating dates: {}'.format(traceback.format_exc()))

            # Generating table (optional)
            tables = None

            # Storing in a global json
            global_bre_fields_json[page_block_nr] = bre_fields_json


        print('*****************************')
        print(global_bre_fields_json)
        print('*****************************')

        # Taking common fields from the first room
        final_bre_fields_json = global_bre_fields_json[1]

        if tables:
            for table_data in tables:
                table_name = table_data[0]
                table_fields = table_data[1]

                if table_name:
                    # Merging table from the rest of rooms (optional)
                    #for i in range(2,page_block_nr+1):
                    #    final_bre_fields_json[table_name]['value'][str(i)] = global_bre_fields_json[i][table_name]['value'][str(i)]

                    # Removing fields that are already in table (optional)
                    for pop_field in table_fields:
                        try:
                            final_bre_fields_json.pop(pop_field)
                        except:
                            pass

        print('*****************************')
        print(final_bre_fields_json)
        print('*****************************')            
        
        bre_fields_json = final_bre_fields_json                        

        # Updating folios collection in Mongo
        # First, clean json with extracted data
        extracted_data_clean = {}
        for field, value in bre_fields_json.items():
            if isinstance(value['value'], str):
                extracted_data_clean[field] = re.sub(id_regex, '', value['value']).rstrip()
            else:
                extracted_data_clean[field] = {}
                for kk, vv in value['value'].items():
                    extracted_data_clean[field][str(kk)] = {}
                    for kkk, vvv in vv['cells'].items():
                        extracted_data_clean[field][str(kk)][kkk] = vvv['value'].rstrip()

        return bre_fields_json, extracted_data_clean