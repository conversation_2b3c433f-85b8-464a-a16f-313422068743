## Install serverless (Important to use serverless version 3)
npm install -g serverless@3

## Use localstack to test locally
pip install localstack
- localstack start
- localstack stop

## Create the bucket
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

## Check localstack items
aws --endpoint-url=http://localhost:4566 s3 ls
aws --endpoint-url=http://localhost:4566 lambda list-functions
aws --endpoint-url=http://localhost:4566 cloudformation list-stacks

## Roles
The following roles are in the resources section of the template:
- resources/roles...

## Fucntions
Functions are in the resources section of the template:
- resources/functions...

## Layer ouptut
The following layers are in the resources section of the template:
- resources/outputs...

## Scripts
The following scripts are in the scripts section of the template:
- scripts/prepackage.sh
This prepackage.sh script is used to package the lambda functions. It is called by the serverless deploy command from npm run deploy:(prd, qa, local)

## steps functions
The following steps functions are in the stepFunctions section of the template:
- serverless.yml
This file is used to define the step functions.

## Deploy
serverless deploy --stage local

## Test
serverless invoke --stage local --function "lambda_name" --data '{event}'

## Localstack Dashboard
https://app.localstack.cloud/dashboard