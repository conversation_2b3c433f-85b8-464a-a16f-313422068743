# Hennessy ARIA - Serverless Email Processing System

AWS Lambda-based serverless system for processing emails, managing documents, and orchestrating workflows for Hennessy Automotive's RPA operations.

## Overview

This serverless application handles:
- **Email Processing**: Monitors and processes incoming emails with attachments
- **Document Management**: Processes invoices, Bills of Lading, and title documents
- **Workflow Orchestration**: AWS Step Functions for complex business processes
- **ARIA Integration**: Creates and manages work items in the ARIA system
- **Data Management**: MongoDB integration for operational data storage

## Architecture

### Core Components

- **Lambda Functions**: Serverless compute for email processing and document handling
- **Step Functions**: Workflow orchestration for complex business processes
- **S3 Storage**: Document and attachment storage
- **MongoDB**: Operational data and workflow state management
- **Secrets Manager**: Secure credential storage

### Key Workflows

1. **Email Processing Workflow** (`process_emails.json`):
   - Email monitoring and attachment extraction
   - Document classification and processing
   - ARIA work item creation

2. **Invoice Processing Workflow**:
   - Invoice download and validation
   - Data extraction and processing
   - Integration with dealer management systems

3. **Title Processing Workflow**:
   - Title document processing
   - SFTP integration for document transfer
   - Status tracking and reporting

## Prerequisites

### System Requirements

- **Node.js**: 16+
- **Python**: 3.11 (for Lambda functions)
- **AWS CLI**: Configured with appropriate permissions
- **Serverless Framework**: Version 3.x

### Required AWS Services

- Lambda
- Step Functions
- S3
- Secrets Manager
- CloudFormation
- ECR (for Docker-based functions)
- CloudWatch (for logging and monitoring)

### External Dependencies

- **MongoDB**: Database for operational data
- **ARIA System**: Document management integration
- **Microsoft Graph API**: For Outlook email access

## Installation

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install Serverless Framework globally (Important: use version 3)
npm install -g serverless@3
```

### 2. Install LocalStack for Local Development

```bash
# Install LocalStack for local testing
pip install localstack
```

## Configuration

### Environment Variables

The system supports multiple deployment stages:

- **local**: LocalStack development environment
- **snd-hen**: Sandbox/development environment
- **prd-hen**: Production environment

### AWS Secrets Manager Configuration

Required secrets for each environment (`{ENV}` = snd-hen or prd-hen):

#### Database Configuration
```json
{
  "name": "{ENV}-mongodb_uri",
  "value": "********************************:port/database"
}
```

#### Email Credentials
```json
{
  "name": "{ENV}-email_credentials",
  "value": {
    "mfa_outlook_config": {
      "client_id": "your-azure-app-client-id",
      "client_secret": "your-azure-app-client-secret",
      "tenant_id": "your-azure-tenant-id",
      "user_id": "<EMAIL>"
    },
    "{ENV}_hennessy_used_cars": {
      "client_id": "used-cars-client-id",
      "client_secret": "used-cars-client-secret",
      "tenant_id": "tenant-id",
      "user_id": "<EMAIL>"
    }
  }
}
```

#### SFTP Credentials
```json
{
  "name": "{ENV}-sftp_credentials",
  "value": {
    "host": "sftp.example.com",
    "username": "sftp_user",
    "password": "sftp_password",
    "port": 22
  }
}
```

### Lambda Function Configuration

Key environment variables set automatically during deployment:

```bash
# Core Configuration
ENV=snd-hen                           # Deployment environment
MONGO_DATABASE=snd_hennessy          # MongoDB database name
BUCKET=snd-hen-bucket                # S3 bucket for document storage

# ARIA Integration
ARIA_ENV=ariahennesy                 # ARIA environment
ARIA_APP_ID_POST_INVENTORY=<app-id>  # ARIA app ID for post-inventory
ARIA_APP_ID_BOLS=<app-id>           # ARIA app ID for Bills of Lading
ARIA_APP_ID_USED_CARS=<app-id>      # ARIA app ID for used cars

# Email Processing
SOURCE_FOLDER=inbox                  # Email source folder
PROCESSED_FOLDER=Processing          # Processed emails folder
UNSORTED_FOLDER=04-Unsorted         # Unsorted emails folder
TOP_FETCH=50                        # Number of emails to fetch
MOVE_EMAILS=false                   # Whether to move processed emails

# Lambda Function References
BRE_LAMBDA=snd-hen-bre              # Business rules engine Lambda
LLM_LAMBDA=snd-hen-llm_extractor    # LLM extraction Lambda
PDF_PROCESSER_LAMBDA=snd-hen-pdf_utils  # PDF processing Lambda
```

## Local Development

### 1. Start LocalStack

```bash
# Start LocalStack services
localstack start

# Stop LocalStack services
localstack stop

# Verify LocalStack is running
localstack status
```

### 2. Create Local S3 Bucket

```bash
# Create deployment bucket for local testing
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

# Verify bucket creation
aws --endpoint-url=http://localhost:4566 s3 ls
```

### 3. Check LocalStack Services

```bash
# List S3 buckets
aws --endpoint-url=http://localhost:4566 s3 ls

# List Lambda functions
aws --endpoint-url=http://localhost:4566 lambda list-functions

# List CloudFormation stacks
aws --endpoint-url=http://localhost:4566 cloudformation list-stacks
```

### 4. Deploy to Local Environment

```bash
# Deploy all functions to LocalStack
npm run deploy:local

# This runs: sh scripts/prepackage.sh && serverless deploy --stage local
```

### 5. Test Local Deployment

```bash
# Test a specific function
serverless invoke --stage local --function "lambda_name" --data '{event}'

# Example: Test email watcher
serverless invoke --stage local --function email_watcher --data '{}'
```

### LocalStack Dashboard

Access the LocalStack dashboard at: https://app.localstack.cloud/dashboard

## Deployment

### Available Deployment Commands

```bash
# Deploy to sandbox environment
npm run deploy:snd

# Deploy to production environment
npm run deploy:prd

# Deploy specific function to sandbox
npm run deploy:snd:function --function=email_watcher

# Deploy specific Step Function to sandbox
npm run deploy:snd:stepfunction --function=process_emails

# Deploy specific function to production
npm run deploy:prd:function --function=email_watcher
```

### Deployment Process

The deployment process consists of:

1. **Prepackaging** (`scripts/prepackage.sh`):
   - Packages Lambda function dependencies
   - Creates deployment artifacts
   - Prepares layers and resources

2. **Serverless Deployment**:
   - Deploys Lambda functions
   - Creates/updates Step Functions
   - Configures IAM roles and policies
   - Sets up CloudWatch logging

## Project Structure

### Roles
The following IAM roles are defined in the resources section:
- `resources/roles/` - Contains all IAM role definitions for Lambda functions

### Functions
Lambda functions are configured in the resources section:
- `resources/functions.{stage}.yml` - Function configurations per deployment stage

### Layers
The following Lambda layers are available:
- `layers/boto3.zip` - AWS SDK layer
- `layers/pymongoAWS.zip` - MongoDB driver layer
- `layers/requests.zip` - HTTP requests layer
- `layers/msal.zip` - Microsoft authentication layer
- `layers/pandas.zip` - Data processing layer

### Scripts
Deployment and utility scripts:
- `scripts/prepackage.sh` - Pre-deployment packaging script
- `scripts_python/deploy.py` - Python deployment utilities

### Step Functions
Workflow definitions in the stepFunctions section:
- `stepfunctions/process_emails.json` - Email processing workflow
- `stepfunctions/process_invoices.json` - Invoice processing workflow
- `stepfunctions/process_titles.json` - Title processing workflow

The Step Functions are defined in `serverless.yml` and deployed automatically.

## Lambda Functions

### Core Functions

1. **email_watcher**: Monitors email accounts for new messages
2. **process_email**: Processes individual emails and attachments
3. **invoice_downloader**: Downloads invoices from dealer systems
4. **pdf_utils**: PDF processing and text extraction
5. **llm_extractor**: AI-powered data extraction from documents
6. **bre**: Business rules engine for document classification
7. **report_to_aria**: Creates work items in ARIA system

### Utility Functions

- **move_email**: Moves processed emails to appropriate folders
- **reconciliate**: Data reconciliation and validation
- **error_wi_report**: Error reporting and notification
- **loading_wi_report**: Work item loading and status updates

## Testing

### Local Testing

```bash
# Test specific function locally
serverless invoke --stage local --function email_watcher --data '{
  "stage": "post-inventory"
}'

# Test with custom event data
serverless invoke --stage local --function process_email --data '{
  "email_id": "test-email-id",
  "stage": "used-cars"
}'
```

### Integration Testing

```bash
# Test Step Function execution
aws stepfunctions start-execution \
  --state-machine-arn "arn:aws:states:region:account:stateMachine:local-process_emails" \
  --input '{"stage": "post-inventory"}'
```

## Monitoring and Logging

### CloudWatch Logs

Lambda functions automatically create log groups:
- `/aws/lambda/{ENV}-email_watcher`
- `/aws/lambda/{ENV}-process_email`
- `/aws/lambda/{ENV}-invoice_downloader`

### Error Handling

- Comprehensive error logging with stack traces
- Email notifications for critical failures
- Retry mechanisms for transient failures
- Dead letter queues for failed messages

## Troubleshooting

### Common Issues

1. **Deployment Failures**:
   ```bash
   # Clean and redeploy
   serverless remove --stage local
   npm run deploy:local
   ```

2. **Function Timeout Issues**:
   - Check CloudWatch logs for performance bottlenecks
   - Increase timeout in function configuration
   - Optimize code for better performance

3. **Permission Errors**:
   - Verify IAM roles have required permissions
   - Check Secrets Manager access policies
   - Ensure S3 bucket permissions are correct

4. **LocalStack Issues**:
   ```bash
   # Reset LocalStack
   localstack stop
   docker system prune -f
   localstack start
   ```

### Debug Mode

Enable debug logging by setting environment variables in function configuration:
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Security

### IAM Roles and Policies

Each Lambda function has a dedicated IAM role with least-privilege permissions:
- S3 bucket access for document storage
- Secrets Manager access for credentials
- MongoDB access through VPC configuration
- CloudWatch Logs for monitoring

### Data Protection

- All credentials stored in AWS Secrets Manager
- Data encrypted at rest and in transit
- VPC configuration for database access
- Secure email authentication with OAuth2

## Support

For technical support:
- **Development Team**: <EMAIL>, <EMAIL>
- **Operations**: <EMAIL>
- **ARIA Support**: <EMAIL>