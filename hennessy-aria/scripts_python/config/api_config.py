def get_api_config(env):
    return [
        {
            "lambda_to_execute": f"{env}-bre_handler",
            "authorizer": f"{env}-lambda_authorizer",
            "action": "POST",
            "endpoint": f"{env}/bre_handler",
            "api_name": f'{env}-hennesy'
        },
        {
            "lambda_to_execute": f"{env}-python_handler",
            "authorizer": f"{env}-lambda_authorizer",
            "action": "POST",
            "endpoint": f"{env}/python_handler",
            "api_name": f'{env}-hennesy'
        },
        {
            "lambda_to_execute": f"{env}-report_to_aria",
            "authorizer": f"{env}-lambda_authorizer",
            "action": "POST",
            "endpoint": f"{env}/report_to_aria",
            "api_name": f'{env}-hennesy'
        },
        {
            "lambda_to_execute": f"{env}-secrets_handler",
            "authorizer": f"{env}-lambda_authorizer",
            "action": "POST",
            "endpoint": f"{env}/secrets_handler",
            "api_name": f'{env}-hennesy'
        },
        {
            "lambda_to_execute": f"{env}-queues_handler",
            "authorizer": f"{env}-lambda_authorizer",
            "action": "POST",
            "endpoint": f"{env}/queues_handler",
            "api_name": f'{env}-hennesy'
        }
    ]