
def get_iam_config():
    return {
        "lambda_authorizer": "resources/roles/lambda_authorizer_role.yml",
        "bre_handler": "resources/roles/bre_handler_role.yml",
        "bre": "resources/roles/bre_role.yml",
        "bre_used_cars": "resources/roles/bre_used_cars_role.yml",
        "bre_pre_inventory": "resources/roles/bre_pre_inventory_role.yml",
        "llm_messenger": "resources/roles/llm_messenger_role.yml",
        "pdf_utils": "resources/roles/pdf_utils.yml",
        "email_watcher": "resources/roles/email_watcher_role.yml",
        "move_email": "resources/roles/move_email_role.yml",
        "process_email": "resources/roles/process_email_role.yml",
        "llm_extractor": "resources/roles/llm_extractor_role.yml",
        "llm_extractor_used_cars": "resources/roles/llm_extractor_used_cars_role.yml",
        "llm_extractor_pre_inventory": "resources/roles/llm_extractor_pre_inventory_role.yml",
        "report_sftp_to_s3": "resources/roles/report_sftp_to_s3_role.yml",
        "invoice_downloader": "resources/roles/invoice_downloader_role.yml",
        "invoice_to_aria": "resources/roles/invoice_to_aria_role.yml",
        "title_sftp_to_s3_to_aria": "resources/roles/title_sftp_to_s3_to_aria_role.yml",
        "orchestrator_downloader": "resources/roles/orchestrator_downloader_role.yml",
        "reconciliate": "resources/roles/reconciliate_role.yml",
        "orchestrator_download_update": "resources/roles/orchestrator_download_update_role.yml",
        "reevaluate": "resources/roles/reevaluate_role.yml",
        "python_handler": "resources/roles/python_handler_role.yml",
        "loading_wi_report": "resources/roles/loading_wi_report_role.yml",
        "error_wi_report": "resources/roles/error_wi_report_role.yml",
        "report_loaded_data": "resources/roles/report_loaded_data_role.yml",
        "queues_handler": "resources/roles/queues_handler_role.yml",
        "secrets_handler": "resources/roles/secrets_handler_role.yml",
        "report_pages_not_used_in_titles": "resources/roles/report_pages_not_used_in_titles_role.yml",
        "report_to_aria": "resources/roles/report_to_aria_role.yml",
        "load_pricing_guide": "resources/roles/load_pricing_guide_role.yml",
        "load_pricing_guide_extractor": "resources/roles/load_pricing_guide_extractor_role.yml",
        "pre_stock_in_vins": "resources/roles/pre_stock_in_vins_role.yml",

        "process_price_guide_workflow": "resources/roles/process_pricing_guide_workflow_role.yml",
        "process_email_workflow": "resources/roles/process_email_workflow_role.yml",
        "download_invoices_workflow": "resources/roles/download_invoices_workflow_role.yml",
        "process_titles_workflow": "resources/roles/process_titles_workflow_role.yml",
        "process_invoices_workflow": "resources/roles/process_invoices_workflow_role.yml",
        "download_new_vehicles_reports_workflow": "resources/roles/download_new_vehicles_reports_workflow_role.yml",

    }