import boto3
import sys
import time

class LambdaHandler():
    def __init__(self, profile):
        self.boto3 = boto3.Session(profile_name=profile, region_name='us-east-1')

    def get_account_id(self):
        # Initialize the STS client
        sts_client = self.boto3.client('sts')
        identity = sts_client.get_caller_identity()
        return identity['Account']

    def create_or_update_lambda_function(
        self, function_name, description, zip_file_path, handler, role_name, runtime,
        security_group_ids, subnet_ids, environment_variables, timeout, memory_size, layers,
        max_retries=5, backoff_factor=2):
        
        # Initialize the Lambda client
        lambda_client = self.boto3.client('lambda')

        # Open the deployment package ZIP file
        with open(zip_file_path, 'rb') as f:
            zip_data = f.read()

        try:
            # Check if the function already exists
            lambda_client.get_function(FunctionName=function_name)
            # Function exists, update it
            retry_attempts = 0
            while retry_attempts < max_retries:
                try:
                    response = lambda_client.update_function_code(
                        FunctionName=function_name,
                        ZipFile=zip_data,
                    )
                    waiter = lambda_client.get_waiter('function_updated')
                    waiter.wait(FunctionName=function_name)

                    lambda_client.update_function_configuration(
                        FunctionName=function_name,
                        Description=description,
                        Handler=handler,
                        Role=f"arn:aws:iam::{self.get_account_id()}:role/{role_name}",
                        Runtime=runtime,
                        Timeout=timeout,
                        MemorySize=memory_size,
                        VpcConfig={
                            'SubnetIds': subnet_ids,
                            'SecurityGroupIds': security_group_ids
                        },
                        Environment={
                            'Variables': environment_variables
                        },
                        Layers=layers
                    )
                    break
                except lambda_client.exceptions.ResourceConflictException as e:
                    print(f"Conflict detected: {e}. Retrying...")
                    retry_attempts += 1
                    time.sleep(backoff_factor ** retry_attempts)
                    if retry_attempts == max_retries:
                        print("Max retries reached. Exiting...")
                        sys.exit(1)
        except lambda_client.exceptions.ResourceNotFoundException:
            # Function does not exist, create it
            response = lambda_client.create_function(
                FunctionName=function_name,
                Description=description,
                Runtime=runtime,
                Handler=handler,
                Role=f"arn:aws:iam::{self.get_account_id()}:role/{role_name}",
                Code={'ZipFile': zip_data},
                Timeout=timeout,
                MemorySize=memory_size,
                VpcConfig={
                    'SubnetIds': subnet_ids,
                    'SecurityGroupIds': security_group_ids
                },
                Environment={
                    'Variables': environment_variables
                },
                Layers=layers
            )

        # Print the ARN of the created or updated function
        return response['FunctionArn']

