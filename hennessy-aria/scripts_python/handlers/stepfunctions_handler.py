import boto3
import sys
import json


class StepFunctionsHandler():
    
    def __init__(self, profile):
        self.boto3 = boto3.Session(profile_name=profile, region_name='us-east-1')
        
    def get_account_id(self):
        # Initialize the STS client
        sts_client = self.boto3.client('sts')
        identity = sts_client.get_caller_identity()
        return identity['Account']

    def create_or_update_step_function(
        self, env, name, definition_json_path, role_name):

        with open(definition_json_path, 'r') as f:
            definition = f.read()
        
        # Initialize the Step Functions client
        sfn_client = self.boto3.client('stepfunctions')
        region = self.boto3.region_name or 'us-east-1'

        account_id = self.get_account_id()

        definition = definition.replace('##REGION##', region)
        definition = definition.replace('##ACCOUNT_ID##', account_id)
        definition = definition.replace('##ENV##', env)

        try:
            # Check if the state machine already exists
            response = sfn_client.describe_state_machine(
                stateMachineArn=f'arn:aws:states:us-east-1:{account_id}:stateMachine:{name}'
            )
            # State machine exists, update it
            sfn_client.update_state_machine(
                stateMachineArn=response['stateMachineArn'],
                definition=definition,
                roleArn=f"arn:aws:iam::{self.get_account_id()}:role/{role_name}"
            )

        except sfn_client.exceptions.StateMachineDoesNotExist:
            # State machine does not exist, create it
            response = sfn_client.create_state_machine(
                name=name,
                definition=definition,
                roleArn=f"arn:aws:iam::{self.get_account_id()}:role/{role_name}",
                type='STANDARD'  # or 'EXPRESS', based on your use case
            )

