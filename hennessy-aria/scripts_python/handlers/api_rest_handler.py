import boto3
import time

class APIGatewayRestHandler:
    def __init__(self, profile, region='us-east-1'):
        self.session = boto3.Session(profile_name=profile, region_name=region)
        self.region = region

    def get_account_id(self):
        sts_client = self.session.client('sts')
        return sts_client.get_caller_identity()['Account']

    def create_or_get_rest_api(self, api_name):
        apig = self.session.client('apigateway')
        apis = apig.get_rest_apis()['items']
        for api in apis:
            if api['name'] == api_name:
                print(f"Found existing REST API: {api['id']}")
                return api['id']
        print(f"Creating new REST API: {api_name}")
        resp = apig.create_rest_api(
            name=api_name,
            endpointConfiguration={'types': ['PRIVATE']}
        )
        print(f"Created REST API: {resp['id']}")
        return resp['id']

    def get_root_resource_id(self, apig, rest_api_id):
        resources = apig.get_resources(restApiId=rest_api_id)['items']
        for res in resources:
            if res['path'] == '/':
                return res['id']
        raise Exception("Root resource not found")

    def create_or_get_resource_for_path(self, rest_api_id, full_path):
        """Supports multi-segment paths like 'env/secrets_handler'."""
        apig = self.session.client('apigateway')
        segments = [seg for seg in full_path.split('/') if seg]
        parent_id = self.get_root_resource_id(apig, rest_api_id)
        for seg in segments:
            curr_id = None
            resources = apig.get_resources(restApiId=rest_api_id)['items']
            for res in resources:
                if res.get('pathPart') == seg and res.get('parentId') == parent_id:
                    curr_id = res['id']
                    break
            if not curr_id:
                resp = apig.create_resource(
                    restApiId=rest_api_id,
                    parentId=parent_id,
                    pathPart=seg
                )
                curr_id = resp['id']
            parent_id = curr_id
        return parent_id

    def create_or_get_lambda_authorizer(self, rest_api_id, authorizer_name, lambda_arn):
        apig = self.session.client('apigateway')
        authorizers = apig.get_authorizers(restApiId=rest_api_id).get('items', [])
        for a in authorizers:
            if a['name'] == authorizer_name:
                print(f"Using existing authorizer {a['id']} ({authorizer_name})")
                return a['id']
        print(f"Creating authorizer {authorizer_name} for {lambda_arn}")
        resp = apig.create_authorizer(
            restApiId=rest_api_id,
            name=authorizer_name,
            type='REQUEST',
            authorizerUri=f"arn:aws:apigateway:{self.region}:lambda:path/2015-03-31/functions/{lambda_arn}/invocations",
            identitySource='method.request.header.Authorization',
        )
        self.add_lambda_permission_for_authorizer(lambda_arn.split(":")[-1], rest_api_id)
        return resp['id']

    def add_lambda_permission_for_authorizer(self, lambda_function_name, rest_api_id):
        lambda_client = self.session.client('lambda')
        statement_id = f'apigateway-authorizer-{rest_api_id}'
        try:
            lambda_client.add_permission(
                FunctionName=lambda_function_name,
                StatementId=statement_id,
                Action='lambda:InvokeFunction',
                Principal='apigateway.amazonaws.com',
                SourceArn=f'arn:aws:execute-api:{self.region}:{self.get_account_id()}:{rest_api_id}/authorizers/*'
            )
            print(f"Added authorizer permission for Lambda: {lambda_function_name}")
        except lambda_client.exceptions.ResourceConflictException:
            print(f"Permission already exists for Lambda: {lambda_function_name}")
        except Exception as e:
            print(f"Error adding Lambda permission: {e}")

    def add_lambda_permission_for_integration(self, lambda_function_name, rest_api_id):
        lambda_client = self.session.client('lambda')
        statement_id = f'apigateway-integration-{rest_api_id}'
        try:
            lambda_client.add_permission(
                FunctionName=lambda_function_name,
                StatementId=statement_id,
                Action='lambda:InvokeFunction',
                Principal='apigateway.amazonaws.com',
                SourceArn=f'arn:aws:execute-api:{self.region}:{self.get_account_id()}:{rest_api_id}/*/*'
            )
            print(f"Added integration permission for Lambda: {lambda_function_name}")
        except lambda_client.exceptions.ResourceConflictException:
            print(f"Integration permission already exists for Lambda: {lambda_function_name}")
        except Exception as e:
            print(f"Error adding Lambda permission: {e}")

    def setup_method_with_authorizer(self, rest_api_id, resource_id, http_method, lambda_arn, authorizer_id):
        apig = self.session.client('apigateway')

        # Create method with the authorizer
        try:
            apig.put_method(
                restApiId=rest_api_id,
                resourceId=resource_id,
                httpMethod=http_method,
                authorizationType='CUSTOM',
                authorizerId=authorizer_id,
                apiKeyRequired=False,
            )
        except apig.exceptions.ConflictException:
            print(f"Method {http_method} already exists on resource {resource_id}")
        except Exception as e:
            print(f"Error putting method: {e}")

        # Integration with Lambda
        uri = f"arn:aws:apigateway:{self.region}:lambda:path/2015-03-31/functions/{lambda_arn}/invocations"
        try:
            apig.put_integration(
                restApiId=rest_api_id,
                resourceId=resource_id,
                httpMethod=http_method,
                type='AWS_PROXY',
                integrationHttpMethod='POST',
                uri=uri
            )
        except apig.exceptions.ConflictException:
            print(f"Integration already exists for {http_method} on {resource_id}")
        except Exception as e:
            print(f"Error putting integration: {e}")

        # Method Response
        try:
            apig.put_method_response(
                restApiId=rest_api_id,
                resourceId=resource_id,
                httpMethod=http_method,
                statusCode='200',
                responseModels={'application/json': 'Empty'}
            )
        except apig.exceptions.ConflictException:
            pass

        # Integration Response
        try:
            apig.put_integration_response(
                restApiId=rest_api_id,
                resourceId=resource_id,
                httpMethod=http_method,
                statusCode='200',
                selectionPattern=''
            )
        except apig.exceptions.ConflictException:
            pass

    def deploy_api(self, rest_api_id, stage='prod'):
        apig = self.session.client('apigateway')
        try:
            apig.create_deployment(
                restApiId=rest_api_id,
                stageName=stage
            )
            print(f"Deployed to stage: {stage}")
        except apig.exceptions.ConflictException:
            print(f"Deployment already exists for stage: {stage}")