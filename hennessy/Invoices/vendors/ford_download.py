import re
import time
from bs4 import BeautifulSoup
import os
import sys
import json

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from Invoices.downloader import Downloader

class FORD(Downloader):
    def __init__(self, store, current_flow):
        super().__init__(store, current_flow)
        # vin
        self.vin = ''
        self.base_url = 'https://www.fmcdealer.dealerconnection.com/content/fmcdealer/us/en/Sales/VehicleSalesPrograms/VehicleInvoices.html'
        self.chrome.navigate_to_url(self.base_url)

        self.label = 'ford'

    def get_last_url(self):
        """Get the last URL from the logs"""
        logs = self.chrome.driver.get_log("performance")
        last_url = None
        for log in logs:
            message = json.loads(log["message"])
            if "message" in message:
                if "method" in message["message"]:
                    if message["message"]["method"] == "Network.requestWillBeSent":
                        last_url = message["message"]["params"]["documentURL"]
        return last_url
    
    def send_headers(self):
        self.chrome.run_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Referer": "https://www.ford.com/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
                "Accept-Language": "en-US,en;q=0.9"
            }
        })

        # Erase webdriver from the website
        self.chrome.run_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            """
        })


    def login(self, user, password):
        # login
        self.stage = 'LOGIN'
        try:
            time.sleep(2)
            self._log_info('Signing in...')
            # Ford page is blocking chrome in headless mode
            if self.chrome.headless:
                self.send_headers()
                self.chrome.navigate_to_url(self.base_url)
                
            if not self.chrome.type_value('ID','userName', user):
                # Try refreshing the page and retry
                self.chrome.refresh_page()
                time.sleep(2)
                self._log_info('Retrying login...')
                self.chrome.type_value('ID','userName', user)
            

            time.sleep(2)
            self.chrome.click_button('XPATH','//input[@type = "submit" and @value = "Next"]')
            time.sleep(2)
            self.chrome.type_value('ID','password', password, password=True)
            time.sleep(2)

            self.chrome.click_button('ID','btn-sign-in', action=True)
            time.sleep(10)
            self.chrome.get_element('XPATH','//span[text()="Dealer, Supplier, Other Login"]')
            if not self.chrome.click_button('XPATH','//span[text()="Dealer, Supplier, Other Login"]'):
                try:
                    self.chrome.refresh_page()
                    time.sleep(2)
                    assert self.chrome.click_button('XPATH','//span[text()="Dealer, Supplier, Other Login"]')
                except Exception as e:
                    # Print logs
                    logs = self.chrome.driver.get_log("performance")
                    for log in logs:
                        print(log)

                    raise e
                    
                raise Exception('Could not complete login')
            self._log_info('Succeded!')
        except Exception as e:
            self._handle_error(e)

    def search_by_vin(self, vin, count):
        """Search invoice"""
        self.stage = 'SEARCH_INVOICE'
        try:
            self.vin = vin
            if count > 1:
                # Inspect page
                self.chrome.click_button('ID', 'BackHyperLink')
                self._log_info("Navigating back to search page...")
            else:
                self.chrome.click_button('XPATH','//a[@href="/DealerStmt/vehinvsearchcustom.asp"]')
            self._log_info(f"Searching for VIN: {vin}")
            self.chrome.type_value('XPATH','//*[@name="VINTEXT"]', vin[-8:]) # last 8 digits
            self.chrome.click_button('XPATH','//img[@onclick="return SearchFields()"]')
            invoice_found = False
            try:
                invoice_found = self.chrome.get_element('XPATH','//*[@id="GVResults_ctl02_PreviewImageButton"]')
            except:
                # Try returning to previous page and retry
                self._log_info("Page ran into an error. Retrying...")
                self.chrome.back_arrow()
                self.utilities.sleep(2)
                self.chrome.type_value('XPATH','//*[@name="VINTEXT"]', vin[-8:])
                self.chrome.click_button('XPATH','//img[@onclick="return SearchFields()"]')
                invoice_found = self.chrome.get_element('XPATH','//*[@id="GVResults_ctl02_PreviewImageButton"]')
            if not invoice_found:
                return False # VIN not found
            self._log_info("VIN found!")
            return True # VIN found
        except Exception as e:
            self._handle_error(e)

    def save_invoice(self):
        """Download invoice"""
        self.stage = 'SAVE_INVOICE'
        try:
            html = self.chrome.get_page_source()
            soup = BeautifulSoup(html, 'html.parser')

            input_element = soup.find('input', {'id': 'GVResults_ctl02_PreviewImageButton'})
            if input_element and 'onclick' in input_element.attrs:
                self._log_info('Starting download...')
                onclick_attr = input_element['onclick']
                match = re.search(r"OnDownload\('Preview','(.*?)','(.*?)','(.*?)','(.*?)','(.*?)'\)", onclick_attr)

                if match:
                    self._log_info('Download button found')
                    values = match.groups()
                    hid_selected = ",".join(values)

                    base_url = "https://fordvisions.dealerconnection.com/DealerStmt/VehInvDownload.aspx"
                    full_url = f"{base_url}?hid_selected={hid_selected}|"

                    # Navigate to trigger download
                    self.chrome.navigate_to_url(full_url)
                    self.utilities.sleep(2)  # Allow navigation delay
                    self._log_info('Download triggered')

                    self.get_file_from_download()
                    return True
                
            raise Exception('Download button not found')
        except Exception as e:
            self._handle_error(e)