# Hennessy Core RPA Module Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Environment (snd-hen for sandbox, prd-hen for production)
ENV=snd-hen

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=your-aws-account-id

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Configuration (stored in AWS Secrets Manager as {ENV}-mongodb_uri)
MONGO_DATABASE=snd_hennessy
# Note: Actual MongoDB URI should be stored in AWS Secrets Manager

# =============================================================================
# S3 STORAGE CONFIGURATION
# =============================================================================

# S3 Bucket for document storage
S3_BUCKET=snd-hen-bucket

# =============================================================================
# ARIA INTEGRATION
# =============================================================================

# ARIA Environment
ARIA_ENV=ariahennesy

# ARIA Application IDs (different for each stage/document type)
ARIA_APP_ID_POST_INVENTORY=234617d1-59bd-4b2f-b9b4-4ad5de283dd8
ARIA_APP_ID_BOLS=49b382f1-9de3-4372-9388-2e34b5d5c9f7
ARIA_APP_ID_TITLES=caa3d93e-9e96-4549-97a0-a241015b3dbd
ARIA_APP_ID_PRE_INVENTORY=
ARIA_APP_ID_USED_CARS=6a371f66-995c-43ce-a168-05849d04ff7e

# =============================================================================
# SELENIUM CONFIGURATION
# =============================================================================

# Chrome/Selenium Settings
CHROME_HEADLESS=true
CHROME_NO_SANDBOX=true
CHROME_DISABLE_DEV_SHM_USAGE=true
SELENIUM_TIMEOUT=30
SELENIUM_IMPLICIT_WAIT=10

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
DEBUG=false

# =============================================================================
# DEALER SYSTEM CONFIGURATION
# =============================================================================

# Note: Actual credentials are stored in AWS Secrets Manager
# Format: {ENV}-user_login_{store_code}

# Ford Configuration
FORD_STORE_CODE=FOR
# Credentials stored in: snd-hen-user_login_for

# Lexus Configuration  
LEXUS_ATLANTA_STORE_CODE=LOA
LEXUS_GWINNETT_STORE_CODE=LOG
# Credentials stored in: snd-hen-user_login_loa, snd-hen-user_login_log

# JLR Configuration
JLR_NORTH_STORE_CODE=JLRN
JLR_BUCKHEAD_STORE_CODE=JLRB
JLR_GWINNETT_STORE_CODE=JLRG
# Credentials stored in: snd-hen-user_login_jlrn

# Porsche Configuration
PORSCHE_STORE_CODE=POR
PORSCHE_NORTHWEST_STORE_CODE=PNW
# Credentials stored in: snd-hen-user_login_por

# Cadillac Configuration
CADILLAC_STORE_CODE=CAD
# Credentials stored in: snd-hen-user_login_cad

# Honda Configuration
HONDA_STORE_CODE=HON
HONDA_DEALER_NUM=208054
# Credentials stored in: snd-hen-user_login_hon (includes OTP secret)

# Mazda Configuration (under MBG)
MBG_STORE_CODE=MBG
# Credentials stored in: snd-hen-user_login_mbg_mazda

# Manheim Configuration
MANHEIM_STORE_CODE=MANHEIM
# Credentials stored in: snd-hen-user_login_manheim

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Note: Email credentials are stored in AWS Secrets Manager
# Format: {ENV}-email_credentials

# Email processing settings
EMAIL_FETCH_COUNT=50
EMAIL_TIMEOUT=60

# =============================================================================
# PROCESSING STAGES
# =============================================================================

# Available processing stages
STAGE_POST_INVENTORY=post-inventory
STAGE_PRE_INVENTORY=pre-inventory  
STAGE_USED_CARS=used-cars

# =============================================================================
# RETRY AND TIMEOUT CONFIGURATION
# =============================================================================

# Retry settings for failed operations
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY=5
OPERATION_TIMEOUT=300

# Download timeouts
DOWNLOAD_TIMEOUT=120
PAGE_LOAD_TIMEOUT=60

# =============================================================================
# DOCKER CONFIGURATION (for containerized deployment)
# =============================================================================

# Docker image configuration
DOCKER_IMAGE_NAME=pyautomationaws/selenium
ECR_REPOSITORY=your-account-id.dkr.ecr.us-east-1.amazonaws.com/pyautomationaws/selenium
LAMBDA_FUNCTION_NAME=snd-hen-selenium_downloader

# =============================================================================
# DEVELOPMENT/DEBUG SETTINGS
# =============================================================================

# Enable/disable features for development
ENABLE_SCREENSHOTS=false
SAVE_PAGE_SOURCE=false
ENABLE_PERFORMANCE_LOGGING=false

# Local development settings
LOCAL_CHROME_PATH=/usr/bin/google-chrome
LOCAL_CHROMEDRIVER_PATH=/usr/local/bin/chromedriver

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security configurations
VERIFY_SSL=true
ENABLE_AUDIT_LOGGING=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Performance optimization
PARALLEL_PROCESSING=false
MAX_CONCURRENT_DOWNLOADS=3
MEMORY_LIMIT=1024

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Email notifications for errors/reports
NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# =============================================================================
# AWS SECRETS MANAGER REFERENCES
# =============================================================================

# The following secrets should be configured in AWS Secrets Manager:
#
# Database:
# - {ENV}-mongodb_uri
#
# Email Credentials:
# - {ENV}-email_credentials
#
# Store Credentials:
# - {ENV}-user_login_for
# - {ENV}-user_login_loa  
# - {ENV}-user_login_log
# - {ENV}-user_login_jlrn
# - {ENV}-user_login_por
# - {ENV}-user_login_cad
# - {ENV}-user_login_hon
# - {ENV}-user_login_mbg_mazda
# - {ENV}-user_login_manheim
#
# System Configuration:
# - {ENV}-selenium_config
# - {ENV}-sftp_credentials (if using SFTP)
#
# =============================================================================
