FROM python:3.11

# Set environment variables
ENV LAMBDA_TASK_ROOT="/var/task" \
    HOME="/tmp" \
    DEBIAN_FRONTEND=noninteractive

# Allow modifications in HOME
RUN chmod 1777 /tmp

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    libxss1 \
    dos2unix \
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Get the latest version of ChromeDriver
RUN CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d '.' -f 1) \
    && echo "Chrome version: $CHROME_VERSION" \
    && wget -q https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_$CHROME_VERSION -O chrome_version \
    && CHROMEDRIVER_VERSION=$(cat chrome_version) \
    && echo "ChromeDriver version: $CHROMEDRIVER_VERSION" \
    && wget -q https://storage.googleapis.com/chrome-for-testing-public/$CHROMEDRIVER_VERSION/linux64/chromedriver-linux64.zip \
    && unzip chromedriver-linux64.zip \
    && mv chromedriver-linux64/chromedriver /usr/local/bin/ \
    && rm -rf chromedriver-linux64.zip chromedriver-linux64 chrome_version

# Check if Chrome and ChromeDriver versions match
RUN CHROME_MAJOR=$(google-chrome --version | awk '{print $3}' | cut -d '.' -f 1) \
    && CHROMEDRIVER_MAJOR=$(chromedriver --version | awk '{print $2}' | cut -d '.' -f 1) \
    && echo "Chrome major version: $CHROME_MAJOR" \
    && echo "ChromeDriver major version: $CHROMEDRIVER_MAJOR" \
    && if [ "$CHROME_MAJOR" != "$CHROMEDRIVER_MAJOR" ]; then echo "VERSION MISMATCH"; exit 1; fi

# Create function directory
RUN mkdir -p ${LAMBDA_TASK_ROOT}

WORKDIR ${LAMBDA_TASK_ROOT}

# Install awslambdaric
RUN pip install --no-cache-dir --target ${LAMBDA_TASK_ROOT} awslambdaric

# Install requirements
COPY requirements.txt ${LAMBDA_TASK_ROOT}
RUN pip install --no-cache-dir -r requirements.txt

# Copy necessary files
COPY . ${LAMBDA_TASK_ROOT}/

# Set up the entrypoint
COPY ./start.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
RUN dos2unix /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["app.lambda_handler"]