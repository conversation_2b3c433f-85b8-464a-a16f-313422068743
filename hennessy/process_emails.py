import invokeRPA as invokeRPA

import os
import argparse
import requests
import urllib.parse
import msal
from datetime import datetime

# --- Configuration for Microsoft Graph API ---
IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'

# --- Utility function to print error details ---
def print_error_details(message, response):
    print(message)
    print('Status code:', response.status_code)
    try:
        print('Response content:', response.json())
    except Exception:
        print('Response content:', response.text)



# --- Helper function to save an email as an EML file ---
def save_email_as_eml(outlook_api, email, output_dir):
    """
    Downloads the MIME content for the email and saves it as an .eml file.
    """
    email_id = email["id"]
    mime_content = outlook_api.get_email_mime(email_id)
    if not mime_content:
        print(f"Skipping email {email_id} due to MIME download failure.")
        return None

    # Create a safe file name using the subject (or email id)
    subject = email.get("subject", "NoSubject")
    safe_subject = "".join(c for c in subject if c.isalnum() or c in " _-").strip()
    if not safe_subject:
        safe_subject = email_id

    filename = os.path.join(output_dir, f"{safe_subject}_{email_id}.eml")
    with open(filename, "wb") as f:
        f.write(mime_content)
    print(f"Saved EML: {filename}")
    return filename

# --- Main script ---
def main():
    parser = argparse.ArgumentParser(
        description="Download emails as EML files (suitable for Outlook on macOS)."
    )
    parser.add_argument('--start', type=str,
                        help="Start datetime (UTC) in ISO format (e.g. 2025-02-05T08:59:00Z)")
    parser.add_argument('--end', type=str,
                        help="End datetime (UTC) in ISO format (e.g. 2025-02-06T09:00:00Z)")
    parser.add_argument('--count', type=int,
                        help="Download the last N emails (if no date range is provided)")
    parser.add_argument('--output', type=str, default="output",
                        help="Output directory for EML files")
    args = parser.parse_args()

    # ======= CONFIGURATION =======
    # Replace these with your actual Azure AD app credentials and mailbox ID.
    client_id = "986fa2ea-d6a8-4eaf-9aef-6d7ac9fa5c5a"
    client_secret = "****************************************"
    tenant_id = "b132204c-7588-4734-9db4-5235da6e5c03"
    user_id = "<EMAIL>"  # This can be the mailbox email address or a GUID

    outlook_api = invokeRPA.Outlook(client_id, client_secret, tenant_id, user_id)

    if not os.path.exists(args.output):
        os.makedirs(args.output)

    emails = None
    count = 50 # how many emails to download
    emails = outlook_api.get_emails_from_folder("Inbox", top_fetch=count)


    if not emails:
        print("No emails were found with the specified criteria.")
        return

    print(f"Found {len(emails)} emails; processing them now...")

    # Process each email: save as .eml file.
    for email in emails:
        save_email_as_eml(outlook_api, email, args.output)

if __name__ == "__main__":
    main()