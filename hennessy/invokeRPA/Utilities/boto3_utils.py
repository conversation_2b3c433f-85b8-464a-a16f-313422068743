import boto3
from botocore.exceptions    import ClientError
import os
class Boto3Utils():
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.sm = boto3.client('secretsmanager')

        # aws_access_key_id = "********************"
        # aws_secret_access_key = "4wQke6cCnOOBlEXUzVFuixKMV62fpCoFY54Wwjfd"
        # aws_session_token = "IQoJb3JpZ2luX2VjEDIaDGNhLWNlbnRyYWwtMSJGMEQCIBgiJxb89pNcRLtHxbqVq2VOGXuC2n7KWsUsW+b9uxnGAiBNf1OHB2duJA8caRnKLiVP7QuMJtBJ+8M1/lk3sZgBESqZAwgrEAAaDDQ0MzM3MDcxOTgwOSIMFxxKl2t1oQommVTOKvYCzwZUZ0KIxQrcJzvwk9ILlNdXqKyPKdL5HcgZzQLQtNnQqYfn8nfSisUF2hCiW/0snAscThcr+iNvP/XNsSvWQgiIPbungbjlf+ca3skXduv2a9DBOMdOD4Wsiat5rw8rpU0VwPfQohSls+LqQjlsm4E2Jsyz26JAli353h105V6webfV7SgnbZRtmRpzVFovIEpX+atIq14Yhpd6yNpR/3OrjbAtbosly28CZq9RR/uFHqvHqAwYYPCPSkhk6kLwtV6FIdX5M/6MI3ZBP1F0dmRWZTTVxjLsmGmTX/3t8kpWaDo6ieaFicGVqFiR5bJvlxnLA6c4p7hTg/el0Bwk8ISMD2r/bM4hm8pbwRRHsiRkYu0ADt7TjVyqrf0+JC4TZHWwbYfECTll+Avb0Y7oheU1tmjuw3AhjacdBuSXzvsWlZWWldAsQalsjwE88J8ZZmbebRYKhzx6P/iOZzEX9Jga0ZLqvap7RdjewcncG418PDwdp8Qw+/HpwgY6pwGUeVLr+dhBxHoySOfVQ+3kcYPkgA9C4Lga5YjL3l+tlwI+FjxuzBmB488KcYBsGD22d4btC9DGxTLEejXRaq1m2EoOj5OvXFqL9j0MpZkzpjWYyUcLg/iR3nUX7ZFJ7Ft7to5qrO/ENI3V1gvftDE/vtisLy3ak4bEajQjas01tMWCLzI3QYpwGBr/o2LzhYYzvP309wlf89BvCDOpjaQjVfjuDgNOxQ=="
        # region_name = "us-east-1"

        # self.s3 = boto3.client(
        #     's3',
        #     aws_access_key_id=aws_access_key_id,
        #     aws_secret_access_key=aws_secret_access_key,
        #     aws_session_token=aws_session_token,
        #     region_name=region_name
        # )
        # self.sm = boto3.client(
        #     'secretsmanager',
        #     aws_access_key_id=aws_access_key_id,
        #     aws_secret_access_key=aws_secret_access_key,
        #     aws_session_token=aws_session_token,
        #     region_name=region_name
        # )

    def upload_file(self, file_path, bucket, key):
        try:
            self.s3.upload_file(file_path, bucket, key)
        except ClientError as e:
            raise e
        
    def get_secret(self, secret_name):
        try:
            # Attempt to get the specified secret
            response = self.sm.get_secret_value(SecretId=secret_name)
            return response['SecretString']

        except ClientError as e:
            print(f"Error retrieving secret '{secret_name}': {e}")
            raise e
        
    def update_secret(self, name, value):
        try:
            response = self.sm.update_secret(
                SecretId=name,
                SecretString=value
            )
            if response.get('ResponseMetadata',''):
                return True if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else False
            else:
                return False            
        except Exception as e:
            print(f'Error: {e}')
            return False