import os
import time
import uuid
import logging
import tempfile
import traceback
import selenium.webdriver               as wd

from selenium.webdriver                 import <PERSON><PERSON>hai<PERSON>
from selenium.webdriver.support         import expected_conditions as EC
from selenium.webdriver.support.ui      import Select
from selenium.webdriver.support.wait    import Web<PERSON>riverWait
from selenium.webdriver.common.by       import By
from selenium.webdriver.common.keys     import Keys

from bs4 import BeautifulSoup
from selenium.common.exceptions import StaleElementReferenceException
import base64

logger = logging.getLogger('selenium')
logger.setLevel(logging.INFO)
logger.addHandler(logging.StreamHandler())

os.environ['HOME'] = '/tmp'
tempdir_userdata = tempfile.mkdtemp(prefix=f"chrome-data-{uuid.uuid4().hex}-")
tempdir_cache = tempfile.mkdtemp(prefix=f"chrome-cache-{uuid.uuid4().hex}-")

# Logging relevant information about path
logger.debug(f'Home directory: {os.environ["HOME"]}')
logger.debug(f'Temp directory for user data: {tempdir_userdata}')
logger.debug(f'Temp directory for cache: {tempdir_cache}')
logger.debug(f'Os.getcwd: {os.getcwd()}')


class ChromeBrowserSelenium:
    def __init__(self, logger, store):
        self.driver = None
        self.logger = logger
        self.timeout = 10
        self.store = store

    def __del__(self):
        if self.driver:
            self.driver.quit()
        self.logger.debug('Quitting browser and ending session')

    def launch_browser(self, headless=False, store='default'):
        try:
            self.headless = headless
            print("Current directory: ", os.getcwd())
            download_path=os.getcwd() + "/" + store

            # Assert folder exists
            os.makedirs(download_path, exist_ok=True)
            
            options = wd.ChromeOptions()
            options.binary_location = "/usr/bin/google-chrome"
            #options.binary_location = "/Applications/Google Chrome.app"
            #options.binary_location = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"

            if self.headless:
                options.add_argument("--headless=new")
                options.add_argument("--hide-scrollbars")
            
            options.add_argument("--disable-component-update")
            options.add_argument("--remote-debugging-port=9222")
            options.add_argument("--disable-background-networking")
            options.add_argument("--disable-default-apps")
            options.add_argument("--incognito")
            options.add_argument("--no-first-run")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-setuid-sandbox")
            options.add_argument("--no-zygote")

            # Data and cache directories
            options.add_argument(f"--user-data-dir={tempdir_userdata}")
            options.add_argument(f"--disk-cache-dir={tempdir_cache}")
            options.add_argument("--crash-dumps-dir=/tmp")

            # TLS/SSL options
            options.add_argument("--ignore-certificate-errors")
            options.add_argument("--ssl-version-min=tls1.2")
            options.add_argument("--cipher-suite=0x009c,0x009d,0x002f,0x0035")

            # Hide headless and automation from the website
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--enable-features=NetworkService")
            if self.store != "MGB":
                options.add_argument("--disable-web-security")
                options.add_argument("--disable-site-isolation-trials")
                options.add_argument("--disable-features=IsolateOrigins,site-per-process")
            options.add_argument("--disable-site-isolation-trials")
            options.add_argument("--disable-3d-apis")
            options.add_argument("--disable-webgl")

            # Header options
            options.add_argument("--lang=en-US")
            options.add_argument("--window-size=1366,768")
            options.add_argument("--force-device-scale-factor=1")
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
            options.add_argument(f"user-agent={user_agent}")
            
            # Disable automation features
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Download preferences
            options.add_experimental_option("prefs", {
                "download.default_directory": os.path.abspath(download_path),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,
                "plugins.always_open_pdf_externally": True
            })

            # Logging preferences
            options.set_capability("goog:loggingPrefs", {"performance": "ALL", "browser": "ALL"})

            options.set_capability("goog:chromeOptions", {
                "args": options.arguments,
                "prefs": options.experimental_options.get("prefs", {}),
                "mobileEmulation": {"deviceName": "Desktop"}
            })

            # Initialize the browser
            print("Initializing Chrome...")
            self.driver = wd.Chrome(options=options)
            print("Session ID: ", self.driver.session_id)
            print("Browser launched successfully")
            print("Browser capabilities: ", self.driver.capabilities)

            # Initialize the action chain
            self.actions = ActionChains(self.driver)

            # Configure the browser to download files to the specified path
            params = {
                "behavior": "allow",
                "downloadPath": download_path
            }
            self.driver.execute_cdp_cmd("Page.setDownloadBehavior", params)

            self.logger.debug(f'Browser launched successfully with downloads enabled at {download_path}')

            try:
                self.driver.get('https://www.google.com')
                self.logger.debug('Successfully loaded google.com')
            except:
                return False
            return True
        except Exception as e:
            print(traceback.format_exc())
            self.logger.error(f'Error in method launch_browser: {e}')
            return False
    
    def get_page_source(self):
        return self.driver.page_source

    def quit_browser(self):
        try:
            self.driver.quit()
        except Exception as e:
            self.logger.error(f'Error in method quit_browser: {e}')
            return False
        self.logger.debug('Quitting browser')
        return True

    def navigate_to_url(self, url, new_tab=False):
        try:
            if new_tab:
                # Open a new tab
                self.driver.execute_script("window.open('');")
                # Switch to the new tab
                self.driver.switch_to.window(self.driver.window_handles[-1])

            self.driver.get(url)
            self.driver.set_window_size(1920, 1080)
            time.sleep(2)
        except Exception as e:
            self.logger.error(f'Error in method navigate_to_url: {e}')
            return False

        self.logger.debug('Navigate to ' + url)
        return True

    def click_button(self, by, value, action=False):
        by = self.__get_by(by)
        try:
            button = WebDriverWait(self.driver, self.timeout).until(EC.presence_of_element_located((by, value)))
            if action:
                print("Fake human click")
                self.actions.move_to_element(button).click().perform()
            else:
                button.click()
        except Exception as e:
            self.logger.error(f'Error in method click_button: {e}')
            return False
        self.logger.debug('Clicked button ' + value)
        return True

    def __get_by(self, by):
        if by == 'ID':
            return By.ID
        elif by == 'XPATH':
            return By.XPATH
        elif by == 'NAME':
            return By.NAME
        elif by == 'CLASS_NAME':
            return By.CLASS_NAME
        else:
            return By.ID

    def switch_to_next_tab(self):
        try:
            # Get all window handles
            window_handles = self.driver.window_handles
            if len(window_handles) > 1:
                self.driver.switch_to.window(window_handles[-1])  # Switch to the latest tab
                self.logger.debug('Switched to the newly opened tab')
                return True
            else:
                self.logger.debug('No new tab detected')
                return False
        except Exception as e:
            self.logger.error(f'Error in method switch_to_next_tab: {e}')
            return False

    def type_value(self, by, value, text, password=False):
        by = self.__get_by(by)
        data = text
        display_value = '********' if password else text
        try:
            field = WebDriverWait(self.driver, self.timeout).until(EC.presence_of_element_located((by, value)))
            field.click()
            field.clear()
            field.send_keys(data)
        except Exception as e:
            self.logger.error(f'Error in method type_value: {e}')
            return False
        self.logger.debug(f'Typed value in {value}')
        return True

    def send_keys(self, by, value, key, text=None):
        by = self.__get_by(by)

        # Mapping of human-readable key names to Selenium's Keys module
        key_map = {
            "BACKSPACE": Keys.BACKSPACE,
            "BS": Keys.BACKSPACE,
            "BKSP": Keys.BACKSPACE,
            "BREAK": Keys.CANCEL,
            "DELETE": Keys.DELETE,
            "DEL": Keys.DELETE,
            "DOWN": Keys.ARROW_DOWN,
            "END": Keys.END,
            "ENTER": Keys.RETURN,
            "~": Keys.RETURN,
            "ESC": Keys.ESCAPE,
            "HELP": Keys.HELP,
            "HOME": Keys.HOME,
            "INSERT": Keys.INSERT,
            "INS": Keys.INSERT,
            "LEFT": Keys.ARROW_LEFT,
            "PGDN": Keys.PAGE_DOWN,
            "PGUP": Keys.PAGE_UP,
            "RIGHT": Keys.ARROW_RIGHT,
            "TAB": Keys.TAB,
            "UP": Keys.ARROW_UP,
            "F1": Keys.F1, "F2": Keys.F2, "F3": Keys.F3, "F4": Keys.F4,
            "F5": Keys.F5, "F6": Keys.F6, "F7": Keys.F7, "F8": Keys.F8,
            "F9": Keys.F9, "F10": Keys.F10, "F11": Keys.F11, "F12": Keys.F12,
            "ADD": Keys.ADD,
            "SUBTRACT": Keys.SUBTRACT,
            "MULTIPLY": Keys.MULTIPLY,
            "DIVIDE": Keys.DIVIDE,
            "SHIFT": Keys.SHIFT,
            "CTRL": Keys.CONTROL,
            "ALT": Keys.ALT,
        }

        # Validate if the key exists in the dictionary
        if key not in key_map:
            self.logger.error(f'Error: The key "{key}" is not a valid option.')
            return False

        try:
            field = WebDriverWait(self.driver, self.timeout).until(EC.presence_of_element_located((by, value)))

            if text:
                field.send_keys(text)  # Type the provided text before sending the key

            field.send_keys(key_map[key])  # Press the specified key
        except Exception as e:
            self.logger.error(f'Error in method send_keys: {e}')
            return False

        self.logger.debug(f'Sent key "{key}" to element {value}')
        return True

    def get_element(self, by, value, error=True):
        by = self.__get_by(by)
        try:
            field = WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((by, value)))
            self.logger.debug('Got element ' + value)
            return field
        except Exception as e:
            if error:
                self.logger.error(f'Error in method get_element: {e}')
            return False

    def take_screenshot(self, filename):
        try:
            self.driver.save_screenshot(filename)
        except Exception as e:
            self.logger.error(f'Error in method take_screenshot: {e}')
            return False
        self.logger.debug('Took screenshot ' + filename)
        return True

    def set_timeout(self, timeout):
        self.timeout = timeout
        self.logger.debug('Set timeout to ' + str(timeout))
        return True

    def switch_tab(self, num):
        handles = self.driver.window_handles
        print(handles)
        self.driver.switch_to.window(handles[num])

    def switch_frame(self, name):
        handles = self.driver.window_handles
        worked = False
        for idx in range(len(handles)):
            if not worked:
                try:
                    self.driver.switch_to.window(handles[idx])
                    self.driver.switch_to.frame(name)
                    worked = True
                except:
                    pass
        return worked

    def find_and_switch_frame(self, by, value):
        by = self.__get_by(by)
        try:
            frame = WebDriverWait(self.driver, self.timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            self.driver.switch_to.frame(frame)
            self.logger.debug(f'Switched to frame "{value}"')
            return True
        except Exception as e:
            self.logger.error(f'Error in method find_and_switch_frame: {e}')
            return False

    def close_window(self):
        self.driver.close()

    def download_html(self):
        page_source = self.driver.page_source

        # Optionally, you can save it to a file to inspect it
        with open("page_source.html", "w") as file:
            file.write(page_source)

    def get_dropdown_options(self, by, val):
        by_val = self.__get_by(by)
        dropdown = Select(self.driver.find_element(by_val, val))  # Replace 'dropdown_id' with the actual ID
        return dropdown, dropdown.options

    def get_options(self, by, val):
        by_val = self.__get_by(by)
        options = self.driver.find_elements(by_val, val)
        return options

    def get_table_elements(self, table, by, val):
        by_val = self.__get_by(by)
        elements = table.find_elements(by_val, val)
        return elements

    def switch_to_default(self):
        self.driver.switch_to.default_content()

    def back_arrow(self):
        self.driver.back()

    def select_option_by_value(self, by, val, option):
        by = self.__get_by(by)
        try:
            # Get dropdown element
            dropdown = WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((by, val))
            )
            # Select the option
            select = Select(dropdown)
            select.select_by_value(option)

            self.logger.debug(f'Selected option "{option}" in dropdown "{val}"')
            return True
        except Exception as e:
            self.logger.error(f'Error in method select_option: {e}')
            return False

    def select_custom_dropdown_option(self, dropdown_xpath, option_text):
        try:
            # Click to open dropdown
            dropdown = WebDriverWait(self.driver, self.timeout).until(
                EC.element_to_be_clickable((By.XPATH, dropdown_xpath))
            )
            dropdown.click()
            self.logger.debug('Clicked dropdown to open options')

            # Wait and click the desired option
            option_xpath = f'//li[@role="option" and @aria-label="{option_text}"]'
            option = WebDriverWait(self.driver, self.timeout).until(
                EC.element_to_be_clickable((By.XPATH, option_xpath))
            )
            time.sleep(1)
            option.click()
            self.logger.debug(f'Selected option "{option_text}"')
            return True
        except Exception as e:
            self.logger.error(f'Error in custom dropdown select: {e}')
            return False

    def save_frame_as_pdf(self, frame_name, output_path):
        # Switch to frame
        self.driver.switch_to.default_content()
        self.driver.switch_to.frame(frame_name)

        # Get frame HTML
        frame_html = self.driver.page_source

        # Create a new tab with just that HTML
        self.driver.switch_to.default_content()
        self.driver.execute_script("window.open('about:blank', '_blank');")
        self.driver.switch_to.window(self.driver.window_handles[-1])

        script = f"""
                document.open();
                document.write(`{frame_html}`);
                document.close();
            """
        self.driver.execute_script(script)
        time.sleep(1)
        # Print to PDF
        pdf = self.driver.execute_cdp_cmd("Page.printToPDF", {
            "printBackground": True
        })

        # Save to file
        with open(output_path, "wb") as f:
            f.write(base64.b64decode(pdf['data']))

        # Clean up
        self.driver.close()
        self.driver.switch_to.window(self.driver.window_handles[0])
        return frame_html
    
    def save_html_as_pdf(self, output_path):

        # Acceder al comando de impresión
        pdf = self.driver.execute_cdp_cmd("Page.printToPDF", {
            "printBackground": True,
            "paperWidth": 8.27,     # A4 en pulgadas
            "paperHeight": 11.69
        })

        # Guardar el PDF como archivo
        with open(output_path, "wb") as f:
            f.write(base64.b64decode(pdf['data']))

        

    def refresh_page(self):
        try:
            self.driver.refresh()
            self.logger.debug('Page refreshed')
            return True
        except Exception as e:
            self.logger.error(f'Error in method refresh_page: {e}')
            return False
        
    def run_script(self, script):
        try:
            self.driver.execute_script(script)
            self.logger.debug('Script executed')
            return True
        except Exception as e:
            self.logger.error(f'Error in method run_script: {e}')
            return False
        
    def get_var_value_script(self, script):
        try:
            return self.driver.execute_script(script)
        except Exception as e:
            self.logger.error(f'Error in method run_script: {e}')
            return None

    def run_cmd(self, obj, command):
        try:
            self.driver.execute_cdp_cmd(obj, command)
            self.logger.debug('Command executed')
            return True
        except Exception as e:
            self.logger.error(f'Error in method run_cmd: {e}')
            return False
        
    def get_cookies(self):
        try:
            cookies = self.driver.get_cookies()
            self.logger.debug('Got cookies')
            return cookies
        except Exception as e:
            self.logger.error(f'Error in method get_cookies: {e}')
            return False
        
    def apply_cookie(self, cookie):
        try:
            self.driver.add_cookie(cookie)
            self.logger.debug('Cookie applied')
            return True
        except Exception as e:
            self.logger.error(f'Error in method apply_cookie: {e}')
            return False
        

    def extract_element_text(self, by, value, clean_html=True):
        """
        Safely extracts text from a given element. Optionally strips HTML using BeautifulSoup.
        :param by: Locator strategy ('ID', 'XPATH', etc.)
        :param value: Locator value
        :param clean_html: Whether to clean innerHTML using BeautifulSoup
        :return: Extracted text or None on failure
        """
        by_val = self.__get_by(by)

        for attempt in range(3):  # Retry up to 3 times
            try:
                element = WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((by_val, value))
                )

                html = element.get_attribute("innerHTML")
                if clean_html:
                    soup = BeautifulSoup(html, "html.parser")
                    return soup.get_text(separator=" ", strip=True)
                else:
                    return html
            except StaleElementReferenceException:
                time.sleep(0.5)
                continue
            except Exception as e:
                self.logger.error(f'Error in method extract_element_text: {e}')
                return None
        return None  # If all retries fail