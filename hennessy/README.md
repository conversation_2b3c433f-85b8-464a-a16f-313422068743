# Hennessy Automotive RPA System

A comprehensive robotic process automation (RPA) system for Hennessy Automotive dealerships that automates invoice processing, email handling, and document management across multiple automotive brands and systems.

## Overview

This system consists of three main components:

1. **hennessy** - Core RPA automation for invoice downloading and processing
2. **hennessy-aria** - AWS Lambda-based serverless email processing and workflow orchestration
3. **hennessy-rpa-reynolds** - Reynolds and Reynolds system integration for vehicle data management

## Features

- **Multi-Brand Support**: Ford, Lexus, JLR (Jaguar Land Rover), Porsche, Cadillac, Honda, Mazda, and more
- **Automated Invoice Processing**: Downloads and processes invoices from various dealer management systems
- **Email Automation**: Processes emails with attachments and creates work items in ARIA system
- **Document Management**: Handles Bills of Lading (BoL), invoices, and title documents
- **AWS Integration**: Serverless architecture with Lambda functions, Step Functions, and S3 storage
- **MongoDB Integration**: Stores and manages processing data and workflow states
- **Selenium Automation**: Web scraping and automation for dealer portals

## Architecture

### Core Components

- **Email Processing Pipeline**: Monitors mailboxes, processes attachments, creates ARIA work items
- **Invoice Downloader**: Automated invoice retrieval from dealer management systems
- **Document Processor**: Handles various document types (invoices, BoLs, titles)
- **Workflow Orchestration**: AWS Step Functions for complex business processes
- **Data Storage**: MongoDB for operational data, S3 for document storage

### Supported Dealer Systems

- **Ford**: Ford dealer portal integration
- **Lexus**: Lexus dealer management system
- **JLR**: Jaguar Land Rover systems (JLRN, JLRB, JLRG)
- **Porsche**: Porsche dealer portal (POR, PNW)
- **Cadillac**: Cadillac dealer system
- **Honda**: Honda dealer portal with OTP authentication
- **Mazda**: Mazda dealer system
- **Manheim**: Auction platform integration

## Prerequisites

### System Requirements

- **Python**: 3.11+
- **Node.js**: 16+ (for serverless deployment)
- **Docker**: For containerized Lambda functions
- **Chrome/Chromium**: For Selenium automation
- **AWS CLI**: Configured with appropriate permissions

### Required Services

- **AWS Account** with the following services:
  - Lambda
  - Step Functions
  - S3
  - Secrets Manager
  - ECR (Elastic Container Registry)
  - CloudFormation
- **MongoDB**: Database for operational data
- **ARIA System**: Document management system integration
- **Microsoft Azure AD**: For Outlook email integration

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd hennessy
```

### 2. Install Python Dependencies

```bash
# Core hennessy module
cd hennessy
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Reynolds integration
cd ../hennessy-rpa-reynolds
pip install -r requirements.txt
```

### 3. Install Node.js Dependencies

```bash
cd hennessy-aria
npm install
```

### 4. Install Serverless Framework

```bash
npm install -g serverless@3
```

## Configuration

### Environment Variables

The following environment variables must be configured:

#### Core System
```bash
ENV=snd-hen                    # Environment (snd-hen, prd-hen)
MONGO_DATABASE=snd_hennessy    # MongoDB database name
```

#### AWS Configuration
```bash
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=<your-account-id>
```

#### Email Configuration (stored in AWS Secrets Manager)
- `{ENV}-email_credentials`: Outlook/Exchange credentials
- `{ENV}-mongodb_uri`: MongoDB connection string

#### Store Credentials (stored in AWS Secrets Manager)
- `{ENV}-user_login_for`: Ford dealer credentials
- `{ENV}-user_login_loa`: Lexus dealer credentials
- `{ENV}-user_login_jlrn`: JLR dealer credentials
- `{ENV}-user_login_por`: Porsche dealer credentials
- `{ENV}-user_login_cad`: Cadillac dealer credentials
- `{ENV}-user_login_hon`: Honda dealer credentials
- `{ENV}-user_login_mbg_mazda`: Mazda dealer credentials

#### ARIA Integration
```bash
ARIA_ENV=ariahennesy
ARIA_APP_ID_POST_INVENTORY=<app-id>
ARIA_APP_ID_BOLS=<app-id>
ARIA_APP_ID_USED_CARS=<app-id>
```

### AWS Secrets Manager Setup

Create the following secrets in AWS Secrets Manager:

1. **Database Connection**:
   ```json
   {
     "name": "{ENV}-mongodb_uri",
     "value": "********************************:port/database"
   }
   ```

2. **Email Credentials**:
   ```json
   {
     "name": "{ENV}-email_credentials",
     "value": {
       "mfa_outlook_config": {
         "client_id": "your-client-id",
         "client_secret": "your-client-secret",
         "tenant_id": "your-tenant-id",
         "user_id": "<EMAIL>"
       }
     }
   }
   ```

3. **Store Credentials** (example for Ford):
   ```json
   {
     "name": "{ENV}-user_login_for",
     "value": {
       "user": "username",
       "actual_password": "password"
     }
   }
   ```

### Local Development Setup

#### Using LocalStack for Local Testing

```bash
# Install LocalStack
pip install localstack

# Start LocalStack
localstack start

# Create S3 bucket for local testing
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

# Check LocalStack services
aws --endpoint-url=http://localhost:4566 s3 ls
aws --endpoint-url=http://localhost:4566 lambda list-functions
```

## Deployment

### 1. Build and Deploy Core Lambda (Docker-based)

```bash
cd hennessy
chmod +x build_deploy.sh
./build_deploy.sh
```

### 2. Deploy Serverless Functions

```bash
cd hennessy-aria

# Deploy to local (LocalStack)
npm run deploy:local

# Deploy to sandbox
npm run deploy:snd

# Deploy to production
npm run deploy:prd

# Deploy specific function
npm run deploy:snd:function --function=email_watcher
```

## Usage

### Email Processing

The system automatically monitors configured email accounts and processes incoming emails with attachments:

```python
# Manual email processing
python process_emails.py --count 50 --output ./output
```

### Invoice Download

Download invoices for specific VINs:

```python
# Example Lambda event
{
    "store": "FOR",
    "action": "invoice_download",
    "stage": "post-inventory",
    "data": {
        "vins": ["1FTFW1ET5DFC12345"]
    }
}
```

### Supported Actions

- `invoice_download`: Download invoices for specified VINs
- `download_honda_pricing_guide`: Download Honda pricing guides
- `download_new_vehicles_report`: Download new vehicle reports
- `reset_password`: Reset dealer portal passwords

## Project Structure

```
hennessy/
├── hennessy/                   # Core RPA module
│   ├── app.py                 # Main Lambda handler
│   ├── process_emails.py      # Email processing utility
│   ├── Invoices/              # Invoice processing modules
│   ├── BoL/                   # Bill of Lading processing
│   ├── invokeRPA/             # RPA utilities and drivers
│   └── workers/               # Background workers
├── hennessy-aria/             # Serverless email processing
│   ├── src/                   # Lambda function sources
│   ├── stepfunctions/         # Step Function definitions
│   ├── resources/             # CloudFormation resources
│   └── scripts/               # Deployment scripts
└── hennessy-rpa-reynolds/     # Reynolds system integration
    ├── HEN_Utilities/         # Hennessy-specific utilities
    ├── Process_Utils/         # Process automation utilities
    └── Utilities/             # Common utilities
```

## Testing

### Running Tests

```bash
# Install test dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Run with coverage
pytest --cov=.
```

### Local Testing with LocalStack

```bash
# Start LocalStack
localstack start

# Deploy to local environment
cd hennessy-aria
npm run deploy:local

# Test Lambda function
serverless invoke --stage local --function email_watcher --data '{}'
```

## Monitoring and Logging

### CloudWatch Logs

Lambda functions automatically log to CloudWatch. Log groups follow the pattern:
- `/aws/lambda/{ENV}-{function-name}`

### Error Handling

The system includes comprehensive error handling and reporting:
- Failed processes are logged with detailed error messages
- Email notifications for critical failures
- Retry mechanisms for transient failures

## Troubleshooting

### Common Issues

1. **Chrome/ChromeDriver Version Mismatch**:
   ```bash
   # Rebuild Docker image with latest Chrome
   docker build --no-cache -t pyautomationaws/selenium .
   ```

2. **MongoDB Connection Issues**:
   - Verify MongoDB URI in Secrets Manager
   - Check network connectivity and security groups

3. **Email Authentication Failures**:
   - Verify Azure AD app registration
   - Check client credentials in Secrets Manager
   - Ensure proper permissions for mailbox access

4. **Dealer Portal Login Issues**:
   - Verify credentials in Secrets Manager
   - Check for password expiration
   - Review OTP configuration for Honda

### Debug Mode

Enable debug logging by setting environment variables:
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Contributing

1. Follow PEP 8 style guidelines
2. Use pre-commit hooks for code formatting
3. Write tests for new functionality
4. Update documentation for any changes

### Code Quality Tools

```bash
# Install pre-commit hooks
pre-commit install

# Run linting
ruff check .

# Format code
ruff format .
```

## Security Considerations

- All credentials are stored in AWS Secrets Manager
- Lambda functions use least-privilege IAM roles
- Network access is restricted through security groups
- Sensitive data is encrypted at rest and in transit


