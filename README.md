# Hennessy Automotive RPA System

A comprehensive robotic process automation (RPA) system for Hennessy Automotive dealerships that automates invoice processing, email handling, document management, and dealer system integration across multiple automotive brands.

## 🚗 Overview

This enterprise-grade RPA system streamlines automotive dealership operations through:

- **Multi-Brand Automation**: Support for Ford, Lexus, JLR, Porsche, Cadillac, Honda, Mazda, and more
- **Email Processing Pipeline**: Automated email monitoring, attachment processing, and workflow orchestration
- **Document Management**: Intelligent processing of invoices, Bills of Lading, and title documents
- **Dealer System Integration**: Direct integration with Reynolds and Reynolds and other dealer management systems
- **Cloud-Native Architecture**: AWS Lambda-based serverless infrastructure with MongoDB storage
- **AI-Powered Extraction**: LLM-based document data extraction and classification

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Hennessy RPA System                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   hennessy/     │  │ hennessy-aria/  │  │hennessy-rpa-    │ │
│  │                 │  │                 │  │reynolds/        │ │
│  │ Core RPA        │  │ Serverless      │  │                 │ │
│  │ Automation      │  │ Email           │  │ Reynolds        │ │
│  │                 │  │ Processing      │  │ Integration     │ │
│  │ • Invoice DL    │  │                 │  │                 │ │
│  │ • Web Scraping  │  │ • AWS Lambda    │  │ • Vehicle Data  │ │
│  │ • Selenium      │  │ • Step Functions│  │ • Title Mgmt    │ │
│  │ • Multi-Brand   │  │ • S3 Storage    │  │ • Pre-Inventory │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │                       │
            ┌───────▼────────┐    ┌────────▼────────┐
            │   MongoDB      │    │   AWS Services  │
            │   Database     │    │                 │
            │                │    │ • Lambda        │
            │ • Operational  │    │ • Step Functions│
            │   Data         │    │ • S3            │
            │ • Workflow     │    │ • Secrets Mgr   │
            │   State        │    │ • CloudWatch    │
            └────────────────┘    └─────────────────┘
```

## 📁 Project Structure

### Core Components

| Component | Purpose | Technology Stack |
|-----------|---------|------------------|
| **[hennessy/](hennessy/)** | Core RPA automation engine | Python 3.11, Selenium, Docker |
| **[hennessy-aria/](hennessy-aria/)** | Serverless email processing | AWS Lambda, Node.js, Python |
| **[hennessy-rpa-reynolds/](hennessy-rpa-reynolds/)** | Reynolds system integration | Python, RPA Framework, TagUI |

### Supported Dealer Systems

- **Ford** - Ford dealer portal integration
- **Lexus** - Lexus dealer management system  
- **JLR** - Jaguar Land Rover systems (JLRN, JLRB, JLRG)
- **Porsche** - Porsche dealer portal (POR, PNW)
- **Cadillac** - Cadillac dealer system
- **Honda** - Honda dealer portal with OTP authentication
- **Mazda** - Mazda dealer system
- **Manheim** - Auction platform integration

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+**
- **Node.js 16+**
- **Docker** (for containerized deployments)
- **AWS CLI** (configured with appropriate permissions)
- **MongoDB** (for operational data)

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd hennessy
   ```

2. **Set up core RPA module**:
   ```bash
   cd hennessy
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. **Set up serverless email processing**:
   ```bash
   cd ../hennessy-aria
   npm install
   npm install -g serverless@3
   ```

4. **Set up Reynolds integration**:
   ```bash
   cd ../hennessy-rpa-reynolds
   pip install -r requirements.txt
   ```

### Configuration

#### Environment Variables

Set the following environment variables:

```bash
# Core Configuration
ENV=snd-hen                    # Environment (snd-hen, prd-hen)
MONGO_DATABASE=snd_hennessy    # MongoDB database name
AWS_REGION=us-east-1          # AWS region

# ARIA Integration
ARIA_ENV=ariahennesy          # ARIA environment
ARIA_APP_ID_POST_INVENTORY=<app-id>
ARIA_APP_ID_BOLS=<app-id>
ARIA_APP_ID_USED_CARS=<app-id>
```

#### AWS Secrets Manager

Configure the following secrets:

- `{ENV}-mongodb_uri` - MongoDB connection string
- `{ENV}-email_credentials` - Email authentication credentials
- `{ENV}-user_login_{store}` - Dealer portal credentials for each store
- `{ENV}-sftp_credentials` - SFTP server credentials

### Local Development

#### Using LocalStack

```bash
# Install and start LocalStack
pip install localstack
localstack start

# Create S3 bucket for testing
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

# Deploy to local environment
cd hennessy-aria
npm run deploy:local
```

## 📋 Usage Examples

### Invoice Download

```python
# Download invoices for specific VINs
lambda_event = {
    "store": "FOR",
    "action": "invoice_download", 
    "stage": "post-inventory",
    "data": {
        "vins": ["1FTFW1ET5DFC12345", "1HGBH41JXMN109186"]
    }
}
```

### Email Processing

```bash
# Process emails manually
cd hennessy
python process_emails.py --count 50 --output ./output
```

### Reynolds Integration

```bash
# Insert vehicle data into Reynolds system
cd hennessy-rpa-reynolds
python insert_vehicle_v2.py
```

## 🔧 Deployment

### Core RPA Module (Docker)

```bash
cd hennessy
chmod +x build_deploy.sh
./build_deploy.sh
```

### Serverless Functions

```bash
cd hennessy-aria

# Deploy to sandbox
npm run deploy:snd

# Deploy to production
npm run deploy:prd

# Deploy specific function
npm run deploy:snd:function --function=email_watcher
```

## 📊 Monitoring & Logging

### CloudWatch Integration

- **Log Groups**: `/aws/lambda/{ENV}-{function-name}`
- **Metrics**: Function duration, error rates, memory usage
- **Alarms**: Automated alerts for failures and performance issues

### Error Handling

- Comprehensive error logging with stack traces
- Email notifications for critical failures
- Retry mechanisms for transient failures
- Dead letter queues for failed messages

## 🔒 Security

### Data Protection

- **Credentials**: All sensitive data stored in AWS Secrets Manager
- **Encryption**: Data encrypted at rest and in transit
- **Access Control**: IAM roles with least-privilege permissions
- **Audit Logging**: Complete audit trail of all operations

### Network Security

- VPC configuration for database access
- Security groups restricting network access
- OAuth2 authentication for email access

## 🧪 Testing

### Unit Tests

```bash
# Run tests for core module
cd hennessy
pytest --cov=.

# Run tests with coverage
pytest --cov=. --cov-report=html
```

### Integration Tests

```bash
# Test Lambda functions locally
cd hennessy-aria
serverless invoke --stage local --function email_watcher --data '{}'
```

### Load Testing

```bash
# Test Step Function execution
aws stepfunctions start-execution \
  --state-machine-arn "arn:aws:states:region:account:stateMachine:process_emails" \
  --input '{"stage": "post-inventory"}'
```

## 🛠️ Troubleshooting

### Common Issues

1. **Chrome/ChromeDriver Version Mismatch**:
   ```bash
   docker build --no-cache -t pyautomationaws/selenium .
   ```

2. **MongoDB Connection Issues**:
   - Verify MongoDB URI in Secrets Manager
   - Check network connectivity and security groups

3. **Email Authentication Failures**:
   - Verify Azure AD app registration
   - Check client credentials in Secrets Manager

4. **LocalStack Issues**:
   ```bash
   localstack stop
   docker system prune -f
   localstack start
   ```

### Debug Mode

Enable debug logging:
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## 📈 Performance Optimization

- **Batch Processing**: Process multiple records efficiently
- **Connection Pooling**: Reuse database connections
- **Lambda Layers**: Shared dependencies reduce deployment size
- **Caching**: Intelligent caching of frequently accessed data

## 🤝 Contributing

1. Follow PEP 8 style guidelines
2. Use pre-commit hooks for code formatting
3. Write tests for new functionality
4. Update documentation for changes

### Code Quality

```bash
# Install pre-commit hooks
pre-commit install

# Run linting
ruff check .

# Format code
ruff format .
```

## 📞 Support

For technical support:
- **Development Team**: <EMAIL>, <EMAIL>
- **Operations**: <EMAIL>
- **ARIA Support**: <EMAIL>

## 📄 License

This project is proprietary software developed for Hennessy Automotive.

---

## 📚 Additional Documentation

- **[Core RPA Module](hennessy/README.md)** - Detailed documentation for the main automation engine
- **[Serverless Email Processing](hennessy-aria/README.md)** - AWS Lambda-based email processing system
- **[Reynolds Integration](hennessy-rpa-reynolds/README.txt)** - Reynolds and Reynolds system integration

---

*Last updated: 2025-01-09*
