#!/bin/bash

# Script to clone Hennessy Auto repositories
# Make sure you have SSH access to GitLab and your SSH key is configured

set -e  # Exit on any error

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Array of repositories to clone
repos=(
    "**************:invokeinc/hennessy-auto/hennessy-aria.git"
    "**************:invokeinc/hennessy-auto/hennessy-aria-used-cars.git"
    "**************:invokeinc/hennessy-auto/hennessy.git"
    "**************:invokeinc/hennessy-auto/hennessy-rpa-pingid.git"
    "**************:invokeinc/hennessy-auto/hennessy-rpa-reynolds.git"
)

# Create a directory for all repositories (optional)
BASE_DIR="hennessy-projects"
if [ ! -d "$BASE_DIR" ]; then
    print_status "Creating directory: $BASE_DIR"
    mkdir -p "$BASE_DIR"
fi

cd "$BASE_DIR"

# Clone each repository
for repo in "${repos[@]}"; do
    # Extract repository name from URL
    repo_name=$(basename "$repo" .git)
    
    print_status "Cloning $repo_name..."
    
    # Check if directory already exists
    if [ -d "$repo_name" ]; then
        print_warning "Directory $repo_name already exists. Skipping..."
        continue
    fi
    
    # Clone the repository
    if git clone "$repo"; then
        print_status "Successfully cloned $repo_name"
    else
        print_error "Failed to clone $repo_name"
        exit 1
    fi
    
    echo ""  # Empty line for readability
done

print_status "All repositories cloned successfully!"
print_status "Repositories are located in: $(pwd)"

# Optional: List cloned directories
echo ""
print_status "Cloned repositories:"
ls -la | grep "^d" | grep -v "^\.$" | grep -v "^\.\.$"