# Hennessy RPA System - Global Environment Variables
# Copy this file to .env and update with your actual values
# This file contains system-wide configuration that applies to all components

# =============================================================================
# GLOBAL SYSTEM CONFIGURATION
# =============================================================================

# Environment (snd-hen for sandbox, prd-hen for production)
ENV=snd-hen

# System Name and Version
SYSTEM_NAME=hennessy-rpa
SYSTEM_VERSION=2.0.0

# =============================================================================
# AWS GLOBAL CONFIGURATION
# =============================================================================

# AWS Account and Region
AWS_ACCOUNT_ID=your-aws-account-id
AWS_REGION=us-east-1

# AWS Profiles per environment
AWS_PROFILE_LOCAL=snd-hen_qa
AWS_PROFILE_SND=snd-hen_qa
AWS_PROFILE_PRD=hen_prd

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Configuration (primary database)
MONGO_DATABASE_SND=snd_hennessy
MONGO_DATABASE_PRD=prd_hennessy
# Note: MongoDB URI stored in AWS Secrets Manager as {ENV}-mongodb_uri

# Reynolds Database (for RPA integration)
REYNOLDS_DB_SERVER=reynolds-server.domain.com
REYNOLDS_DB_NAME=reynolds_db
REYNOLDS_DB_PORT=1433

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# S3 Buckets per environment
S3_BUCKET_SND=snd-hen-bucket
S3_BUCKET_PRD=prd-hen-bucket

# S3 Folder Structure
S3_FOLDER_INVOICES=invoices
S3_FOLDER_INVOICES_NEW_CARS=invoices_new_cars
S3_FOLDER_INVOICES_USED_CARS=invoices_used_cars
S3_FOLDER_BOLS=bol_documents
S3_FOLDER_TITLES=title_documents
S3_FOLDER_REPORTS=reynols_reports
S3_FOLDER_ATTACHMENTS=attachments_files

# =============================================================================
# ARIA INTEGRATION (GLOBAL)
# =============================================================================

# ARIA Environment
ARIA_ENV=ariahennesy

# ARIA Application IDs (Production)
ARIA_APP_ID_POST_INVENTORY_PRD=234617d1-59bd-4b2f-b9b4-4ad5de283dd8
ARIA_APP_ID_BOLS_PRD=49b382f1-9de3-4372-9388-2e34b5d5c9f7
ARIA_APP_ID_TITLES_PRD=caa3d93e-9e96-4549-97a0-a241015b3dbd
ARIA_APP_ID_USED_CARS_PRD=6a371f66-995c-43ce-a168-05849d04ff7e

# ARIA Application IDs (Sandbox - update with actual sandbox IDs)
ARIA_APP_ID_POST_INVENTORY_SND=sandbox-post-inventory-id
ARIA_APP_ID_BOLS_SND=sandbox-bols-id
ARIA_APP_ID_TITLES_SND=sandbox-titles-id
ARIA_APP_ID_USED_CARS_SND=sandbox-used-cars-id

# =============================================================================
# DEALER SYSTEM CONFIGURATION
# =============================================================================

# Supported Dealer Brands
SUPPORTED_BRANDS=FOR,LOA,LOG,JLRN,JLRB,JLRG,POR,PNW,CAD,HON,MBG,MANHEIM

# Ford Configuration
FORD_STORE_CODE=FOR
FORD_PORTAL_URL=https://ford-dealer-portal.com

# Lexus Configuration
LEXUS_ATLANTA_CODE=LOA
LEXUS_GWINNETT_CODE=LOG
LEXUS_PORTAL_URL=https://lexus-dealer-portal.com

# JLR Configuration
JLR_NORTH_CODE=JLRN
JLR_BUCKHEAD_CODE=JLRB
JLR_GWINNETT_CODE=JLRG
JLR_PORTAL_URL=https://jlr-dealer-portal.com

# Porsche Configuration
PORSCHE_CODE=POR
PORSCHE_NORTHWEST_CODE=PNW
PORSCHE_PORTAL_URL=https://porsche-dealer-portal.com

# Cadillac Configuration
CADILLAC_CODE=CAD
CADILLAC_PORTAL_URL=https://cadillac-dealer-portal.com

# Honda Configuration
HONDA_CODE=HON
HONDA_DEALER_NUMBER=208054
HONDA_PORTAL_URL=https://honda-dealer-portal.com

# Mazda Configuration (under MBG)
MBG_CODE=MBG
MAZDA_BRAND=MAZDA
MAZDA_PORTAL_URL=https://mazda-dealer-portal.com

# Manheim Configuration
MANHEIM_CODE=MANHEIM
MANHEIM_PORTAL_URL=https://manheim.com

# =============================================================================
# EMAIL CONFIGURATION (GLOBAL)
# =============================================================================

# Email Processing Settings
EMAIL_FETCH_COUNT=50
EMAIL_TIMEOUT=60
EMAIL_RETRY_COUNT=3

# Email Folders
EMAIL_SOURCE_FOLDER=inbox
EMAIL_PROCESSED_FOLDER=Processing
EMAIL_UNSORTED_FOLDER=04-Unsorted

# Email Accounts (credentials in AWS Secrets Manager)
PRIMARY_EMAIL_ACCOUNT=<EMAIL>
USED_CARS_EMAIL_ACCOUNT=<EMAIL>

# =============================================================================
# PROCESSING STAGES
# =============================================================================

# Available Processing Stages
STAGE_POST_INVENTORY=post-inventory
STAGE_PRE_INVENTORY=pre-inventory
STAGE_USED_CARS=used-cars

# Stage-specific settings
POST_INVENTORY_ENABLED=true
PRE_INVENTORY_ENABLED=false
USED_CARS_ENABLED=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# System Notification Emails
SYSTEM_ADMIN_EMAILS=<EMAIL>,<EMAIL>
DEVELOPER_EMAILS=<EMAIL>,<EMAIL>
ARIA_SUPPORT_EMAIL=<EMAIL>

# Notification Types
ENABLE_SUCCESS_NOTIFICATIONS=false
ENABLE_ERROR_NOTIFICATIONS=true
ENABLE_DAILY_REPORTS=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Settings
ENCRYPT_SENSITIVE_DATA=true
ENABLE_AUDIT_LOGGING=true
VERIFY_SSL_CERTIFICATES=true

# Session Management
SESSION_TIMEOUT=1800
AUTO_LOGOUT_ENABLED=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# System Performance
MAX_CONCURRENT_PROCESSES=5
DEFAULT_TIMEOUT=300
DEFAULT_RETRY_COUNT=3
DEFAULT_RETRY_DELAY=5

# Memory and Resource Limits
MAX_MEMORY_USAGE=2GB
MAX_CPU_USAGE=80%

# =============================================================================
# LOGGING CONFIGURATION (GLOBAL)
# =============================================================================

# Global Logging Settings
LOG_LEVEL=INFO
DEBUG_MODE=false
ENABLE_PERFORMANCE_LOGGING=false

# Log Retention
LOG_RETENTION_DAYS=30
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Health Checks
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=300

# Metrics Collection
ENABLE_METRICS=true
METRICS_INTERVAL=60

# CloudWatch Integration
ENABLE_CLOUDWATCH=true
CLOUDWATCH_LOG_GROUP_PREFIX=/aws/lambda/

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Flags
DEVELOPMENT_MODE=false
ENABLE_MOCK_SERVICES=false
ENABLE_TEST_DATA=false

# Local Development
LOCAL_DEVELOPMENT=false
LOCALSTACK_ENABLED=false
LOCALSTACK_ENDPOINT=http://localhost:4566

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup Settings
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# Recovery Settings
ENABLE_AUTO_RECOVERY=true
RECOVERY_TIMEOUT=600

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================

# External API Integration
ENABLE_EXTERNAL_APIS=true
API_TIMEOUT=30
API_RETRY_COUNT=3

# Webhook Configuration
ENABLE_WEBHOOKS=false
WEBHOOK_TIMEOUT=10

# =============================================================================
# COMPLIANCE AND AUDIT
# =============================================================================

# Compliance Settings
ENABLE_COMPLIANCE_LOGGING=true
AUDIT_LOG_RETENTION_DAYS=365
ENABLE_DATA_ENCRYPTION=true

# Privacy Settings
MASK_SENSITIVE_DATA=true
ENABLE_DATA_ANONYMIZATION=false

# =============================================================================
# AWS SECRETS MANAGER CONFIGURATION
# =============================================================================

# Secrets Manager Secret Names (per environment)
# Database Secrets:
# - {ENV}-mongodb_uri

# Email Secrets:
# - {ENV}-email_credentials

# Dealer Portal Secrets:
# - {ENV}-user_login_for
# - {ENV}-user_login_loa
# - {ENV}-user_login_log
# - {ENV}-user_login_jlrn
# - {ENV}-user_login_por
# - {ENV}-user_login_cad
# - {ENV}-user_login_hon
# - {ENV}-user_login_mbg_mazda
# - {ENV}-user_login_manheim

# System Configuration Secrets:
# - {ENV}-selenium_config
# - {ENV}-sftp_credentials

# =============================================================================
# COMPONENT SPECIFIC SETTINGS
# =============================================================================

# Core RPA Module (hennessy/)
CORE_RPA_ENABLED=true
SELENIUM_HEADLESS=true
CHROME_NO_SANDBOX=true

# Serverless Email Processing (hennessy-aria/)
SERVERLESS_ENABLED=true
LAMBDA_MEMORY_SIZE=256
LAMBDA_TIMEOUT=900

# Reynolds Integration (hennessy-rpa-reynolds/)
REYNOLDS_INTEGRATION_ENABLED=true
RPA_FRAMEWORK_TIMEOUT=30

# =============================================================================
# ENVIRONMENT SPECIFIC OVERRIDES
# =============================================================================

# Uncomment and modify for specific environments:

# Development Environment:
# LOG_LEVEL=DEBUG
# DEBUG_MODE=true
# DEVELOPMENT_MODE=true

# Production Environment:
# LOG_LEVEL=WARNING
# ENABLE_AUDIT_LOGGING=true
# ENCRYPT_SENSITIVE_DATA=true

# =============================================================================
