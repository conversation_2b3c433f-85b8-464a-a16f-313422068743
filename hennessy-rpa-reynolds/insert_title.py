from Process_Utils.reynolds_app import <PERSON><PERSON><PERSON>, PopUpException, PostedVehicleException
from dotenv import load_dotenv
import traceback
from HEN_Utilities.aria_utils import ARIA
import time
from HEN_Utilities.utilities_handler import APIClient
from HEN_Utilities.cars_preprocessing import generate_input_dict
import pyautogui
import base64
from datetime import datetime
import requests
import json

load_dotenv()

API_ENDPOINT = "https://l9axzrmhlk.execute-api.us-east-1.amazonaws.com/snd-hen/"
API_TOKEN = "H9BPXBgmqEK95R2XqVjzJxNa27W3dk"
SECRET = "snd-hen-reynolds_env_vars_snd"

# API_ENDPOINT = "https://3aehxmmp2b.execute-api.us-east-1.amazonaws.com/prd-hen/"
# API_TOKEN = "dwgMcKW9alRxy0HilyapDRlTIA8DCt"
# SECRET = "prd-hen-reynolds_env_vars_prd"

def process_vehicles():    

    api_client = APIClient(
        api_endpoint=API_ENDPOINT,
        api_token=API_TOKEN
    )

    env_vars = api_client.get_secret(SECRET) 
    params = api_client.get_secret(env_vars['CREDENTIALS_SECRET']) 


    aria = ARIA(env_vars["ARIA_BASE_URL"], env_vars["ARIA_TOKEN"], env_vars["ARIA_INVOICE_APP_ID"])

    titles = []
    titles = api_client.get_todays_titles_completed(env_vars["DB_NAME"])
    #if response is not None:
    #    titles = generate_input_dict(response)

    reynolds_closed = False

    if titles:

        reynolds = ReynoldsApp()
        reynolds_closed = True
         
        previous_store = ""
        actual_store = ""
        for index, title in enumerate(titles):
            
            try:
                actual_store = new_car["Brand"] 

                if reynolds_closed == True:
                    reynolds.open_reynolds()
                    reynolds.login(params['user'],params['actual_password'])
                    reynolds_closed = False
                
                if previous_store != actual_store:
                    reynolds.go_to_accounting(actual_store)

                reynolds.insert_new_vehicle(new_car)
                
                aria.bre_reply(new_car["aria_wi_id"], bre_response)
                time.sleep(2)
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="This invoice has been stocked in",
                    status=0
                )
                api_client.update_vin_status_in_mongo(new_car["VIN"], 11, env_vars["DB_NAME"])
                api_client.update_invoice_vin_status(new_car["VIN"], env_vars["DB_NAME"])
            
            except PostedVehicleException:
                print(f"Pop up when doing the stock in of {new_car["VIN"]}: ", traceback.format_exc())
                reynolds.logger.log("INFO", f"Pop up when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")

                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")        
                screenshot = pyautogui.screenshot()
                filename = f"logs/screenshot_{timestamp}.png"
                screenshot.save(filename)

                with open(filename, 'rb') as file:
                    file_content = file.read()
                image_content = base64.b64encode(file_content).decode('utf-8')

                api_client.update_vin_status_in_mongo(new_car["VIN"], 10, env_vars["DB_NAME"])
                aria.bre_reply(
                    new_car["aria_wi_id"], 
                    bre_response={
                        "aria_status":{"value":uuid_needs_status},
                        "aria_exception":{
                            "value":"Error when doing stock in"
                        }, 
                        "invoice": {
                            "stock_in_values":{
                                "display": False
                            },
                            "reynols_report": {
                                "display": False
                            }
                        }
                    }) 
                
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="Vehicle already posted exception",
                    status=1,
                    file = f"data:image/png;base64,{image_content}",
                    file_name = "Already posted vehicle"
                )     

            except PopUpException:
                print(f"Pop up when doing the stock in of {new_car["VIN"]}: ", traceback.format_exc())
                reynolds.logger.log("INFO", f"Pop up when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")

                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")        
                screenshot = pyautogui.screenshot()
                filename = f"logs/screenshot_{timestamp}.png"
                screenshot.save(filename)

                with open(filename, 'rb') as file:
                    file_content = file.read()
                image_content = base64.b64encode(file_content).decode('utf-8')

                api_client.update_vin_status_in_mongo(new_car["VIN"], 9, env_vars["DB_NAME"])      
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="Pop up when doing the stock in",
                    status=1,
                    file = f"data:image/png;base64,{image_content}",
                    file_name = "Pop up screen"
                )     
                
            except Exception:
                print(f"Error when doing the stock in of {new_car["VIN"]}: ", traceback.format_exc())
                reynolds.logger.log("INFO", f"Error when doing the stock in of {new_car["VIN"]}: {traceback.format_exc()}")

                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")        
                screenshot = pyautogui.screenshot()
                filename = f"logs/screenshot_{timestamp}.png"
                screenshot.save(filename)

                with open(filename, 'rb') as file:
                    file_content = file.read()
                image_content = base64.b64encode(file_content).decode('utf-8')

                api_client.update_vin_status_in_mongo(new_car["VIN"], 10, env_vars["DB_NAME"])
                aria.bre_reply(
                    new_car["aria_wi_id"], 
                    bre_response={
                        "aria_status":{"value":uuid_needs_status},
                        "aria_exception":{
                            "value":"Error when doing stock in"
                        }, 
                        "invoice": {
                            "stock_in_values":{
                                "display": False
                            },
                            "reynols_report": {
                                "display": False
                            }
                        }
                    })  
                 
                aria.create_event(
                    item_id=new_car["aria_wi_id"],
                    title="Error when doing the stock in",
                    status=1,
                    file = f"data:image/png;base64,{image_content}",
                    file_name = "Error screen"
                ) 

                reynolds.close_reynolds()
                reynolds_closed = True
                previous_store = ""

            finally:
                previous_store = actual_store

        reynolds.close_reynolds() 

    return processed_vins


if __name__ == "__main__":
    processed_vins = process_vehicles()
    
    if processed_vins:
        headers = {
            'Authorization': f'Bearer {API_TOKEN}',
        }
        body = json.dumps({"vins": processed_vins, "type": "stock_in"})
        response = requests.post(API_ENDPOINT + "report_to_aria", json=body, headers=headers)
