import requests
import time
import os
from dotenv import load_dotenv
import base64

load_dotenv()

 

class ARIA():

	def __init__(self, aria_base_url, aria_token, aria_invoice_app_id):
		self.request_token = aria_token
		self.app_id = aria_invoice_app_id
		self.base_url = aria_base_url

	def request_to_aria(self, type, url, body):
		if type == 'post':
			x = requests.post(url, json = body, headers = {'Authorization': self.request_token, 'Content-Type': 'application/json', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br'})		
		elif type == 'get':
			x = requests.get(url, headers = {'Authorization': self.request_token, 'Content-Type': 'application/json', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br'})		
		else:
			print('Type not supported!')

		print("Resposne --> ", x.text)

		if x.status_code != 202:
			print('!!!!!!!!!!!!!!!!!!!!')
			print(x.status_code)
			print('!!!!!!!!!!!!!!!!!!!!')
			
	def bre_reply(self, item_id, bre_response):
		url = f'{self.base_url}/public/v1/apps/{self.app_id}/case_management_middleware/work_items/{item_id}/bre'
		body = {
			"data":{
				"type":"workItem",
				"id": item_id,
				"attributes":{
					"response": bre_response
				}
			}
			}
		self.request_to_aria('post', url, body)			
	
	def create_item(self, group_name, pdf_base64, app_id):
		url = f'{self.base_url}/public/v1/apps/{app_id}/document_processing'
		body = {
			"data": {
				"attributes": {
					"groups": [
						{
							"name": group_name,
							"content": f"data:application/pdf;base64,{pdf_base64}",

							"metadata": []
						}
					
					]
				},
				"type": "CaseManagement"
			}
		}
		self.request_to_aria('post', url, body)	

	def create_event(self, item_id, title, body=None, status=None, file = None, file_name = None):
		url = f'{self.base_url}/public/v1/apps/{self.app_id}/case_management_middleware/work_items/{item_id}/events'
		request_body = {
			"data": {
				"type": "event",
				"attributes": {
					"title": title,
				}
			}
		}

		if body:
			request_body['data']['attributes']['body'] = body

		if status is not None:
			status_code = 'Completed' if status == 0 else ('Failed' if status == 1 else 'Warning')
			request_body['data']['attributes']['status'] = status_code

		if file:
			request_body['data']['attributes']['file'] = file

		if file_name:
			request_body['data']['attributes']['file_name'] = file_name

		self.request_to_aria('post', url, request_body)

	def exception_aria_reply(self, aria_wi_id, needs_attention_uuid, exception_message, exception_image):
		self.bre_reply(
			aria_wi_id, 
			bre_response={
				"aria_status":{"value":needs_attention_uuid},
				"aria_exception":{
					"value":exception_message
				}, 
				"invoice": {
					"stock_in_values":{
						"display": False
					},
					"reynols_report": {
						"display": False
					}
				}
			}) 

		with open(exception_image, 'rb') as file:
			file_content = file.read()
		image_content = base64.b64encode(file_content).decode('utf-8')

		self.create_event(
			item_id=aria_wi_id,
			title=exception_message,
			status=1,
			file = f"data:image/png;base64,{image_content}",
			file_name = "Exception"
		) 