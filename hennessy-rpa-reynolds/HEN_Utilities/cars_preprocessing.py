
def generate_input_dict(cars_from_mongo):
    cars_vals = []
    for idx, car_vals in enumerate(cars_from_mongo):
        stock_in_vals_cars = car_vals.get("flows").get("post-inventory").get("docs").get("invoice").get("fields").get("stock_in_values").get("rows")
        wi_id = car_vals.get("flows").get("post-inventory").get("docs").get("invoice").get("aria_data").get("aria_wi_id", "")

        car_val_dict = {}
        for k, v in stock_in_vals_cars.items():
            field_name = v.get("cells").get("field").get("value")
            field_val = v.get("cells").get("value").get("value")

            if field_name == "VIN":
                car_val_dict["VIN"] = field_val

            if field_name == "STORE":
                car_val_dict["Store"] = field_val

            if field_name == "BRAND":
                car_val_dict["Brand"] = field_val

            if field_name == "PREFIX":
                car_val_dict["Prefix"] = field_val if field_val != "" else None

            if field_name == "REFERENCE":
                car_val_dict["ReferenceNumber"] = field_val if field_val != "" else None
            
            if field_name == "DATE":
                car_val_dict["Date"] = field_val if field_val != "" else None

            if field_name == "VENDOR #":
                car_val_dict["VendorNumber"] = field_val if field_val != "" else None

            if field_name == "INVOICE AMT":
                car_val_dict["InvoiceAmount"] = str(field_val) if field_val != "" else None

            if field_name == "INVENTORY AMT":
                car_val_dict["InventoryAmount"] = str(field_val) if field_val != "" else None

            if field_name == "HOLDBACK AMT":
                car_val_dict["HoldbackAmount"] = str(field_val) if field_val != "" else None
            
            if field_name == "PDI ALLOWANCE AMT":
                car_val_dict["PdiAllowance"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 1 AMT DT":
                car_val_dict["MFR1AmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 1 AMT CR":
                car_val_dict["MFR1AmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 1 ACCT1-DT":
                car_val_dict["MFR1AccountDT"] = field_val if field_val != "" else None

            if field_name == "MFR 1 ACCT 2-CR":
                car_val_dict["MFR1AccountCR"] = field_val if field_val != "" else None


            if field_name == "MFR 2 AMT DT":
                car_val_dict["MFR2AmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 2 AMT CR":
                car_val_dict["MFR2AmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 2 ACCT 1-DT":
                car_val_dict["MFR2AccountDT"] = field_val if field_val != "" else None

            if field_name == "MFR 2 ACCT 2-CR":
                car_val_dict["MFR2AccountCR"] = field_val if field_val != "" else None


            if field_name == "MFR 3 AMT DT":
                car_val_dict["MFR3AmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 3 AMT CR":
                car_val_dict["MFR3AmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 3 ACCT 1-DT":
                car_val_dict["MFR3AccountDT"] = field_val if field_val != "" else None

            if field_name == "MFR 3 ACCT 2-CR":
                car_val_dict["MFR3AccountCR"] = field_val if field_val != "" else None


            if field_name == "MFR 4 AMT DT":
                car_val_dict["MFR4AmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 4 AMT CR":
                car_val_dict["MFR4AmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "MFR 4 ACCT 1-DT":
                car_val_dict["MFR4AccountDT"] = field_val if field_val != "" else None

            if field_name == "MFR 4 ACCT 2-CR":
                car_val_dict["MFR4AccountCR"] = field_val if field_val != "" else None


            if field_name == "VMS ENTRY":
                car_val_dict["VMSEntry"] = str(field_val) if field_val != "" else None


        car_val_dict["aria_wi_id"] = wi_id
        
        cars_vals.append(car_val_dict)

    return cars_vals

def generate_input_dict_used_cars(cars_from_mongo):
    cars_vals = []
    for idx, car_vals in enumerate(cars_from_mongo):
        stock_in_vals_cars = car_vals.get("flows").get("used-cars").get("docs").get("invoice").get("fields").get("stock_in_values").get("rows")
        wi_id = car_vals.get("flows").get("used-cars").get("docs").get("invoice").get("aria_data").get("aria_wi_id", "")

        car_val_dict = {}
        car_val_dict["Transaction location"] = car_vals.get("flows").get("used-cars").get("docs").get("invoice").get("fields").get("transaction_location").get("value", "")

        for k, v in stock_in_vals_cars.items():
            field_name = v.get("cells").get("field").get("value")
            field_val = v.get("cells").get("value").get("value", "")

            if field_name == "VIN":
                car_val_dict["VIN"] = field_val

            if field_name == "STORE":
                car_val_dict["Store"] = field_val

            if field_name == "BRAND":
                car_val_dict["Brand"] = field_val

            if field_name == "PREFIX":
                car_val_dict["Prefix"] = field_val if field_val != "" else None

            if field_name == "REFERENCE":
                car_val_dict["ReferenceNumber"] = field_val if field_val != "" else None
            
            if field_name == "DATE":
                car_val_dict["Date"] = field_val if field_val != "" else None

            if field_name == "APEX AMOUNT DT":
                car_val_dict["ApexAmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "APEX AMOUNT CR":
                car_val_dict["ApexAmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "APEX ACCT DT":
                car_val_dict["ApexAccountDT"] = field_val if field_val != "" else None

            if field_name == "APEX ACCT CR":
                car_val_dict["ApexAccountCR"] = field_val if field_val != "" else None

            if field_name == "TRANSPORT AMOUNT DT":
                car_val_dict["TransportAmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "TRANSPORT AMOUNT CR":
                car_val_dict["TransportAmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "TRANSPORT ACCT DT":
                car_val_dict["TransportAccountDT"] = field_val if field_val != "" else None

            if field_name == "TRANSPORT ACCT CR":
                car_val_dict["TransportAccountCR"] = field_val if field_val != "" else None

            if field_name == "DOWC AMOUNT DT":
                car_val_dict["DOWCAmountDT"] = str(field_val) if field_val != "" else None

            if field_name == "DOWC AMOUNT CR":
                car_val_dict["DOWCAmountCR"] = str(field_val) if field_val != "" else None

            if field_name == "DOWC ACCT DT":
                car_val_dict["DOWCAccountDT"] = field_val if field_val != "" else None

            if field_name == "DOWC ACCT CR":
                car_val_dict["DOWCAccountCR"] = field_val if field_val != "" else None

            if field_name == "INVENTORY":
                car_val_dict["Inventory"] = str(field_val) if field_val != "" else None

            if field_name == "PAYOFF":
                car_val_dict["Payoff"] = str(field_val) if field_val != "" else ""
            
            if field_name == "PAYABLE":
                car_val_dict["Payable"] = str(field_val) if field_val != "" else None

        car_val_dict["aria_wi_id"] = wi_id
        
        cars_vals.append(car_val_dict)

    return cars_vals

def generate_input_dict_pre_inventory(cars_from_mongo):
    cars_vals = []
    for idx, car_vals in enumerate(cars_from_mongo):
        stock_in_vals_cars = car_vals.get("flows").get("pre-inventory").get("docs").get("invoice").get("fields").get("stock_in_values").get("rows")
        line_items_table = car_vals.get("flows").get("pre-inventory").get("docs").get("invoice").get("fields").get("table").get("rows")
        wi_id = car_vals.get("flows").get("pre-inventory").get("docs").get("invoice").get("aria_data").get("aria_wi_id", "")

        car_val_dict = {}
        car_val_dict["table"] = line_items_table


        for k, v in stock_in_vals_cars.items():
            field_name = v.get("cells").get("field").get("value")
            field_val = v.get("cells").get("value").get("value", "")

            if field_name == "VIN":
                car_val_dict["VIN"] = field_val

            if field_name == "STORE":
                car_val_dict["Store"] = field_val

            if field_name == "BRAND":
                car_val_dict["Brand"] = field_val

            if field_name == "PREFIX":
                car_val_dict["Prefix"] = field_val if field_val != "" else None

            if field_name == "REFERENCE":
                car_val_dict["ReferenceNumber"] = field_val if field_val != "" else None
            
            if field_name == "DATE":
                car_val_dict["Date"] = field_val if field_val != "" else None

            if field_name == "ORDER":
                car_val_dict["Order"] = str(field_val) if field_val != "" else None

            if field_name == "GENERAL COLOR":
                car_val_dict["GeneralColor"] = str(field_val) if field_val != "" else None

            if field_name == "EXTERIOR COLOR":
                car_val_dict["ExteriorColor"] = field_val if field_val != "" else None

            if field_name == "INTERIOR COLOR":
                car_val_dict["InteriorColor"] = field_val if field_val != "" else None

            if field_name == "LIST PRICE":
                car_val_dict["ListPrice"] = str(field_val) if field_val != "" else None

            if field_name == "SALES COST":
                car_val_dict["SalesCost"] = str(field_val) if field_val != "" else None

            if field_name == "BASE MSRP":
                car_val_dict["BaseMRP"] = field_val if field_val != "" else None

        car_val_dict["aria_wi_id"] = wi_id
        
        cars_vals.append(car_val_dict)

    return cars_vals