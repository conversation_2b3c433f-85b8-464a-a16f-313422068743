# Hennessy RPA Reynolds Integration

Python-based RPA automation system for integrating with Reynolds and Reynolds dealer management system for Hennessy Automotive operations.

## Overview

This module provides automated integration with Reynolds and Reynolds systems for:
- **Vehicle Data Management**: Insert and update vehicle information
- **Pre-Inventory Processing**: Handle pre-inventory vehicle data
- **Used Car Management**: Process used car inventory data
- **Title Processing**: Manage vehicle title information
- **Password Management**: Automated password reset functionality

## Features

- **Vehicle Insertion**: Automated insertion of vehicle data into Reynolds system
- **Pre-Inventory Management**: Handle pre-inventory vehicle processing
- **Used Car Processing**: Manage used car inventory operations
- **Title Management**: Process vehicle title information
- **Password Reset**: Automated password reset for Reynolds accounts
- **Utility Functions**: Common utilities for RPA operations

## Prerequisites

### System Requirements

- **Python**: 3.8+
- **Windows OS**: Required for some RPA operations (pywin32 dependency)
- **RPA Framework**: TagUI/RPA library for automation
- **Database Access**: Connection to Reynolds database system

### Required Dependencies

All dependencies are listed in `requirements.txt`:
- `rpa==1.50.0` - Core RPA automation framework
- `tagui==1.50.0` - TagUI automation library
- `pyautogui` - GUI automation
- `pywin32` - Windows-specific operations
- `pandas` - Data processing
- `openpyxl` - Excel file handling
- `pypyodbc` - Database connectivity
- `python-dotenv` - Environment variable management

## Installation

### 1. Install Python Dependencies

```bash
# Install all required packages
pip install -r requirements.txt
```

### 2. Environment Configuration

Create a `.env` file in the project root with required configuration:

```bash
# Database Configuration
DB_SERVER=your-reynolds-server
DB_DATABASE=your-database-name
DB_USERNAME=your-username
DB_PASSWORD=your-password

# RPA Configuration
RPA_TIMEOUT=30
RPA_DELAY=2

# Logging Configuration
LOG_LEVEL=INFO
LOG_PATH=./logs/
```

## Project Structure

```
hennessy-rpa-reynolds/
├── HEN_Utilities/              # Hennessy-specific utilities
├── Process_Utils/              # Process automation utilities
├── Utilities/                  # Common utilities
│   ├── Common_Utilities/       # Shared utility functions
│   │   ├── logger_utility.py   # Logging functionality
│   │   └── env_file_utility.py # Environment file handling
├── config.py                   # Configuration management
├── insert_pre_inventory_car.py # Pre-inventory vehicle insertion
├── insert_title.py            # Title processing
├── insert_used_car.py         # Used car insertion
├── insert_vehicle_v2.py       # Vehicle insertion (v2)
├── reset_reynolds_password.py # Password reset automation
├── requirements.txt           # Python dependencies
└── README.txt                 # This file
```

## Core Scripts

### 1. Vehicle Insertion (`insert_vehicle_v2.py`)

Automated insertion of vehicle data into Reynolds system:

```bash
python insert_vehicle_v2.py
```

**Features**:
- Automated data entry into Reynolds forms
- Error handling and validation
- Logging of all operations
- Support for multiple vehicle types

### 2. Pre-Inventory Processing (`insert_pre_inventory_car.py`)

Handles pre-inventory vehicle data processing:

```bash
python insert_pre_inventory_car.py
```

**Features**:
- Pre-inventory data validation
- Automated form completion
- Status tracking and reporting

### 3. Used Car Management (`insert_used_car.py`)

Processes used car inventory data:

```bash
python insert_used_car.py
```

**Features**:
- Used car data processing
- Inventory status updates
- Price and condition management

### 4. Title Processing (`insert_title.py`)

Manages vehicle title information:

```bash
python insert_title.py
```

**Features**:
- Title document processing
- Status tracking
- Automated data entry

### 5. Password Reset (`reset_reynolds_password.py`)

Automated password reset for Reynolds accounts:

```bash
python reset_reynolds_password.py
```

**Features**:
- Automated password reset process
- Account validation
- Security compliance

## Configuration

### Environment Variables

Required environment variables (set in `.env` file):

```bash
# Database Configuration
DB_SERVER=reynolds-server.domain.com
DB_DATABASE=reynolds_db
DB_USERNAME=rpa_user
DB_PASSWORD=secure_password
DB_PORT=1433

# RPA Settings
RPA_TIMEOUT=30                 # Timeout for RPA operations (seconds)
RPA_DELAY=2                   # Delay between operations (seconds)
RPA_RETRY_COUNT=3             # Number of retry attempts

# Logging Configuration
LOG_LEVEL=INFO                # Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_PATH=./logs/              # Log file directory
LOG_MAX_SIZE=10MB             # Maximum log file size
LOG_BACKUP_COUNT=5            # Number of backup log files

# Reynolds System Configuration
REYNOLDS_URL=https://reynolds-portal.com
REYNOLDS_TIMEOUT=60           # Page load timeout
REYNOLDS_RETRY_DELAY=5        # Retry delay for failed operations
```

### Database Configuration

The system requires access to Reynolds database for:
- Vehicle data retrieval
- Status updates
- Audit logging

Configure database connection in `config.py` or through environment variables.

## Usage

### Basic Usage

1. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Run vehicle insertion**:
   ```bash
   python insert_vehicle_v2.py
   ```

3. **Process pre-inventory**:
   ```bash
   python insert_pre_inventory_car.py
   ```

4. **Handle used cars**:
   ```bash
   python insert_used_car.py
   ```

### Advanced Usage

#### Custom Configuration

```python
from config import Config
from Utilities.Common_Utilities.logger_utility import Logger

# Initialize with custom config
config = Config(config_file='custom_config.py')
logger = Logger(config.LOG_PATH)

# Use in your scripts
logger.info("Starting vehicle processing...")
```

#### Error Handling

```python
try:
    # RPA operations
    result = process_vehicle_data(vehicle_info)
    logger.info(f"Successfully processed vehicle: {result}")
except Exception as e:
    logger.error(f"Error processing vehicle: {str(e)}")
    # Handle error appropriately
```

## Utilities

### Logger Utility (`Utilities/Common_Utilities/logger_utility.py`)

Provides comprehensive logging functionality:

```python
from Utilities.Common_Utilities.logger_utility import Logger

logger = Logger('./logs/')
logger.info("Information message")
logger.error("Error message")
logger.debug("Debug message")
```

### Environment File Utility (`Utilities/Common_Utilities/env_file_utility.py`)

Handles environment variable management:

```python
from Utilities.Common_Utilities.env_file_utility import Env_File

env = Env_File('./logs/', '.env')
values = env.get_values()
db_server = values.get('DB_SERVER')
```

## Troubleshooting

### Common Issues

1. **RPA Framework Issues**:
   ```bash
   # Reinstall RPA framework
   pip uninstall rpa tagui
   pip install rpa==1.50.0 tagui==1.50.0
   ```

2. **Database Connection Issues**:
   - Verify database server accessibility
   - Check credentials and permissions
   - Ensure ODBC drivers are installed

3. **Windows-specific Issues**:
   ```bash
   # Reinstall pywin32
   pip uninstall pywin32
   pip install pywin32
   ```

4. **GUI Automation Issues**:
   - Ensure display is available (not headless)
   - Check screen resolution and scaling
   - Verify pyautogui permissions

### Debug Mode

Enable debug logging:

```bash
# Set in .env file
LOG_LEVEL=DEBUG

# Or set environment variable
export LOG_LEVEL=DEBUG
```

### Log Analysis

Check log files for detailed operation information:

```bash
# View recent logs
tail -f logs/rpa_operations.log

# Search for errors
grep "ERROR" logs/rpa_operations.log
```

## Security Considerations

- **Credential Management**: Store sensitive credentials in environment variables
- **Database Access**: Use least-privilege database accounts
- **Audit Logging**: All operations are logged for audit purposes
- **Error Handling**: Sensitive information is not logged in error messages

## Performance Optimization

- **Batch Processing**: Process multiple records in batches
- **Connection Pooling**: Reuse database connections
- **Timeout Management**: Configure appropriate timeouts
- **Resource Cleanup**: Properly close connections and resources

## Support

For technical support:
- **Development Team**: Check main project README for contact information
- **RPA Issues**: Refer to TagUI/RPA framework documentation
- **Database Issues**: Contact database administrator

## License

This project is proprietary software developed for Hennessy Automotive.
