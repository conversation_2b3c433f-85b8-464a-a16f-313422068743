# Hennessy RPA Reynolds Integration Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Reynolds Database Connection
DB_SERVER=reynolds-server.domain.com
DB_DATABASE=reynolds_db
DB_USERNAME=rpa_user
DB_PASSWORD=your-secure-password
DB_PORT=1433
DB_DRIVER=ODBC Driver 17 for SQL Server

# Database connection pool settings
DB_CONNECTION_TIMEOUT=30
DB_COMMAND_TIMEOUT=60
DB_MAX_POOL_SIZE=10

# =============================================================================
# RPA FRAMEWORK CONFIGURATION
# =============================================================================

# RPA Framework Settings
RPA_TIMEOUT=30
RPA_DELAY=2
RPA_RETRY_COUNT=3
RPA_RETRY_DELAY=5

# TagUI Configuration
TAGUI_TIMEOUT=60
TAGUI_DELAY=1
TAGUI_VISUAL_AUTOMATION=false

# GUI Automation Settings
PYAUTOGUI_PAUSE=1.0
PYAUTOGUI_FAILSAFE=true
SCREENSHOT_ON_ERROR=true

# =============================================================================
# REYNOLDS SYSTEM CONFIGURATION
# =============================================================================

# Reynolds Portal Settings
REYNOLDS_URL=https://reynolds-portal.com
REYNOLDS_LOGIN_URL=https://reynolds-portal.com/login
REYNOLDS_TIMEOUT=60
REYNOLDS_PAGE_LOAD_TIMEOUT=30
REYNOLDS_RETRY_DELAY=5
REYNOLDS_MAX_RETRIES=3

# Reynolds System Credentials (store in environment or secure location)
REYNOLDS_USERNAME=your-reynolds-username
REYNOLDS_PASSWORD=your-reynolds-password
REYNOLDS_DOMAIN=your-domain

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Settings
LOG_LEVEL=INFO
LOG_PATH=./logs/
LOG_FILE_NAME=rpa_operations.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Debug Settings
DEBUG_MODE=false
VERBOSE_LOGGING=false
LOG_SQL_QUERIES=false

# =============================================================================
# VEHICLE PROCESSING CONFIGURATION
# =============================================================================

# Vehicle Data Processing
BATCH_SIZE=10
MAX_CONCURRENT_PROCESSES=3
PROCESS_TIMEOUT=300

# Vehicle Types
VEHICLE_TYPE_NEW=new
VEHICLE_TYPE_USED=used
VEHICLE_TYPE_PRE_INVENTORY=pre_inventory

# VIN Validation
VALIDATE_VIN=true
VIN_LENGTH=17

# =============================================================================
# FILE PROCESSING CONFIGURATION
# =============================================================================

# File Paths
INPUT_DATA_PATH=./data/input/
OUTPUT_DATA_PATH=./data/output/
TEMP_DATA_PATH=./data/temp/
BACKUP_DATA_PATH=./data/backup/

# File Formats
SUPPORTED_EXCEL_FORMATS=.xlsx,.xls
SUPPORTED_CSV_FORMATS=.csv
DEFAULT_ENCODING=utf-8

# File Processing Settings
MAX_FILE_SIZE=50MB
BACKUP_PROCESSED_FILES=true
DELETE_TEMP_FILES=true

# =============================================================================
# ERROR HANDLING CONFIGURATION
# =============================================================================

# Error Handling
CONTINUE_ON_ERROR=true
MAX_CONSECUTIVE_ERRORS=5
ERROR_NOTIFICATION_EMAIL=<EMAIL>

# Retry Configuration
RETRY_ON_DB_ERROR=true
RETRY_ON_NETWORK_ERROR=true
RETRY_ON_GUI_ERROR=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Settings
ENCRYPT_PASSWORDS=true
USE_WINDOWS_AUTH=false
ENABLE_AUDIT_LOGGING=true
MASK_SENSITIVE_DATA=true

# Session Management
SESSION_TIMEOUT=1800
AUTO_LOGOUT=true
FORCE_PASSWORD_CHANGE=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Performance Settings
ENABLE_PARALLEL_PROCESSING=false
MAX_MEMORY_USAGE=2GB
OPTIMIZE_DATABASE_QUERIES=true

# Cache Settings
ENABLE_CACHING=true
CACHE_EXPIRY=3600
CACHE_SIZE=100MB

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Email Notifications
SMTP_SERVER=smtp.company.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_USE_TLS=true

# Notification Recipients
SUCCESS_NOTIFICATION_EMAILS=<EMAIL>
ERROR_NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>
DAILY_REPORT_EMAILS=<EMAIL>

# =============================================================================
# SCHEDULING CONFIGURATION
# =============================================================================

# Scheduled Tasks
ENABLE_SCHEDULER=false
SCHEDULE_INTERVAL=3600
SCHEDULE_START_TIME=08:00
SCHEDULE_END_TIME=18:00

# Business Hours
BUSINESS_HOURS_START=08:00
BUSINESS_HOURS_END=18:00
BUSINESS_DAYS=Monday,Tuesday,Wednesday,Thursday,Friday
TIMEZONE=America/New_York

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# External System Integration
ENABLE_API_INTEGRATION=false
API_BASE_URL=https://api.hennessy.com
API_KEY=your-api-key
API_TIMEOUT=30

# Webhook Configuration
WEBHOOK_URL=https://webhook.hennessy.com/rpa
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=10

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Settings
DEVELOPMENT_MODE=false
MOCK_DATABASE=false
MOCK_REYNOLDS_SYSTEM=false
ENABLE_TEST_DATA=false

# Testing Configuration
TEST_DATA_PATH=./tests/data/
TEST_OUTPUT_PATH=./tests/output/
RUN_INTEGRATION_TESTS=false

# =============================================================================
# WINDOWS SPECIFIC CONFIGURATION
# =============================================================================

# Windows RPA Settings (required for pywin32)
WINDOWS_USER=your-windows-user
WINDOWS_DOMAIN=your-domain
USE_WINDOWS_CREDENTIALS=true

# Windows GUI Settings
SCREEN_RESOLUTION=1920x1080
SCREEN_SCALING=100%
ENABLE_HIGH_DPI=false

# =============================================================================
# BACKUP AND RECOVERY CONFIGURATION
# =============================================================================

# Backup Settings
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL=24
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups/

# Recovery Settings
ENABLE_AUTO_RECOVERY=true
RECOVERY_CHECKPOINT_INTERVAL=10
MAX_RECOVERY_ATTEMPTS=3

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Health Check Settings
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=300
HEALTH_CHECK_TIMEOUT=30

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_INTERVAL=60
TRACK_MEMORY_USAGE=true
TRACK_CPU_USAGE=true

# =============================================================================
# SCRIPT SPECIFIC CONFIGURATION
# =============================================================================

# insert_vehicle_v2.py settings
VEHICLE_INSERT_BATCH_SIZE=5
VEHICLE_INSERT_DELAY=2
VALIDATE_VEHICLE_DATA=true

# insert_pre_inventory_car.py settings
PRE_INVENTORY_BATCH_SIZE=10
PRE_INVENTORY_VALIDATION=true

# insert_used_car.py settings
USED_CAR_BATCH_SIZE=8
USED_CAR_PRICE_VALIDATION=true

# insert_title.py settings
TITLE_BATCH_SIZE=5
TITLE_VALIDATION=true

# reset_reynolds_password.py settings
PASSWORD_RESET_TIMEOUT=60
PASSWORD_COMPLEXITY_CHECK=true
FORCE_PASSWORD_CHANGE_DAYS=90

# =============================================================================
# ENVIRONMENT SPECIFIC OVERRIDES
# =============================================================================

# Development Environment
# Uncomment and modify for development
# LOG_LEVEL=DEBUG
# DEBUG_MODE=true
# MOCK_DATABASE=true

# Production Environment
# Uncomment and modify for production
# LOG_LEVEL=WARNING
# ENABLE_AUDIT_LOGGING=true
# ENCRYPT_PASSWORDS=true

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# To run vehicle insertion:
# python insert_vehicle_v2.py

# To run pre-inventory processing:
# python insert_pre_inventory_car.py

# To run used car processing:
# python insert_used_car.py

# To run title processing:
# python insert_title.py

# To reset Reynolds password:
# python reset_reynolds_password.py

# =============================================================================
