from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.http_utility import Http
from Utilities.Common_Utilities.json_utility import <PERSON><PERSON>
from Common_Utilities.mongo_utility import Mongo
import traceback

class Queues():

    def __init__(self, log_path, token, url, mongo_db_name):
        self.l = Logger(log_path)
        self.http = Http(log_path)
        self.json = Json(log_path)
        self.mongo = Mongo(log_path, token, url)
        self.token = token
        self.url = url
        self.mongo_db_name = mongo_db_name

    #Function that will check if a set of items are already loaded into queue and if they are not, they will be uploaded
    #   INPUT:
    #       -action: insert_one, insert_many, get_one, get_many, update_one, update_many, delete_one, delete_many or get_count
    #       -lock_item: decide if item will be locked or not (Only applies to get_one and get_many)
    #       -tags: tags that item will have (Only applies to insert_one and insert_many)
    #       -key: item_key that item will have (Only applies to insert_one and insert_many)
    #       -mongo_collection_name
    #       -data: information to be uploaded (Only applies to insert_one, insert_many, update_one and update_many)
    #       -filter: specify which items will be affected on the update (Only applies to update_one and update_many)
    #       -limit: number of items that will be returned, by default there is no limit
    #       -sort: field that will be used to sort the items, by default they will be sorted by creation time
    #   OUTPUT:
    #       -items (only applies to get_one and get_many)
    def queue_handler(self, action, lock_item, tags, key, collection_name, data, filter, limit = 0, sort = None):
        try:
            if action in ['insert_one', 'insert_many', 'get_one', 'get_many', 'update_one', 'update_many', 'delete_one', 'delete_many', 'get_count', 'insert_session', 'update_session', 'insert_working_dir', 'update_working_dir', 'get_working_dir']: 
                json = {
                    "action": action,
                    "lock_item": lock_item,
                    "tags": tags,
                    "key": key,
                    "database_name": self.mongo_db_name, 
                    "collection_name": collection_name,
                    "data": data,
                    "filter": filter,
                    "limit": limit
                }
                if sort != None:
                    json['sort_field']= sort
                response = self.mongo.execute_query(json)
            else:
                raise Exception ('Method not valid')
            return response
        except Exception as e:
            raise Exception ('Exception occurred on queue_handler method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Function that will check if a set of items are already loaded into queue and if they are not, they will be uploaded
    #   INPUT:
    #       -data_array: array with item data of all items
    #       -item_key_array: array with item keys of all items
    #       -tag_array_data: array with the tags of the items
    #       -mongo_db_name: name of mongo db
    #       -mongo_collection_name
    #       -skip_loaded: flag to define if we don't need to upload the items that already exist in queue
    def load_queue(self, data_array, item_key_array, item_tag_array, mongo_collection_name, skip_loaded):
        try:
            if skip_loaded:
                filter = {"item_key": {"$in": item_key_array}}

                #CHECK IF ITEMS ARE ON QUEUE
                found_list = self.queue_handler("get_many", False, "", "", mongo_collection_name, filter, "")
                found_list = self.json.load_json(found_list)
                
                #IF LINE ITEMS EXIST, REMOVE EXISTING LINES
                if len(found_list)>0:
                    for found_item in found_list:
                        counter = 0
                        for item_key in item_key_array:
                            if item_key == found_item['item_key']: #If item key appear on mongodb, remove it
                                data_array.pop(counter)
                                item_key_array.pop(counter)
                                item_tag_array.pop(counter)
                            else:
                                counter += 1
            #ADD ITEMS TO QUEUE
            self.queue_handler("insert_many", False, item_tag_array, item_key_array, mongo_collection_name, data_array, "")
        except Exception as e:
            raise Exception ('Exception occurred on load_queue method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Function that will handle the session ids of the execution of the scripts
    #   INPUT:
    #       -action: action to be performed, there are two options, check if a session is running or update the session
    #       -session_id: session to work
    #       -status: status to update (Only applies to action update_session)  
    def handle_session(self, action, session_id, status = ''):
        try:
            data_filter = {"$and":[{"session_id": session_id}, {"session_status":"Running"}]}
            if action == "check_session":   
                item = self.queue_handler("get_one", False, "", "", "", data_filter, "")
                if item == '[null]':
                    raise Exception(f"Session {session_id} not found")
            elif action == "update_session":
                data_update = {
                        "$set":{
                            "session_status": status
                        }
                    }
                self.queue_handler("update_session", False, "", "", "", data_update, data_filter)
        except Exception as e:
            raise Exception('Exception occurred in handle_session method. Details: ' + str(e))