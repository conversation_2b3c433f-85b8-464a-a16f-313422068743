from datetime import datetime
import traceback
import logging

class Logger():

    def __init__(self, log_path):
        self.create_log_file(log_path)

    # Create log file
    #   INPUT:
    #       -log_path: path where log file will be created
    def create_log_file(self, log_path):
        try:
            dt = datetime.today()
            self.log_path = log_path + "\\" + str(dt.year) + str(dt.month) + str(dt.day) + '.log'
            logging.basicConfig(level=logging.INFO, filename= self.log_path, format='%(asctime)s %(levelname)-8s %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logging.error(e)
            raise Exception ('Exception occurred on create_log_file method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Log information
    #   INPUT:
    #       -method: type of log to be used
    #       -text: information to be logged
    def log(self, method, text):
        try:
            match method:
                case "INFO":
                    logging.info(text)
                case "ERROR":
                    logging.error(text)
                case "WARNING":
                    logging.warning(text)
                case "DEBUG":
                    logging.debug(text)
                case "CRITICAL":
                    logging.critical(text)
                case _:
                    raise Exception ("Method {} not valid".format(method))
        except Exception as e:
            logging.error(e)
            raise Exception ('Exception occurred on log method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Extract log information from case
    #   INPUT:
    #       -method: type of log to be used
    #       -text: information to be logged
    def extract_log_from_case(self, item_key):
        try:
            file = open(self.log_path, 'r')
            content = file.read()
            file.close()

            upper_key = "PROCESSING: {}\n".format(item_key)
            lower_key = "PROCESSED: {}\n".format(item_key)

            upper_limit=content.rfind(upper_key)
            lower_limit=content.rfind(lower_key)

            item_log = content[content[:upper_limit].rfind('\n')+1:lower_limit+len(lower_key)]

            return item_log
        except Exception as e:
            logging.error(e)
            raise Exception ('Exception occurred on extract_log_from_case method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))