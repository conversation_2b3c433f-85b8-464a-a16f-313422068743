import os
from Utilities.Common_Utilities.logger_utility import Logger
import pyautogui
import traceback

class Screenshot():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    # Take screenshot
    #   INPUT:
    #       -file_name: filename of screenshot
    #       -folder_path: folder where screenshot will be saved
    #   OUTPUT:
    #       -path: path of the saved screenshot
    def take_screenshot(self, folder_path, file_name):
        try:
            myScreenshot = pyautogui.screenshot()
            path = os.path.join(folder_path, file_name) + '.png'
            myScreenshot.save(path)
            return path
        except Exception as e: 
            raise Exception ('Exception occurred on take_screenshot method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))