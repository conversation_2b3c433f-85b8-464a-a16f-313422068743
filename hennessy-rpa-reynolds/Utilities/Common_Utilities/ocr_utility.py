from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.http_utility import Http
import traceback

class Ocr():

    def __init__(self, log_path):
        self.l = Logger(log_path)
        self.http = Http(log_path)

    #Get text from image
    #   INPUT
    #       -url: url of lambda function
    #       -base64_file: image converted into base64
    #       -token
    #   OUTPUT
    #       -text of image
    def ocr_image(self, url, base64_file, token):
        try:
            body = {
                    "file_b64": base64_file
                }
            response = self.http.http_request('POST', url, body, token)
            return response.text
        except Exception as e:
            raise Exception ('Exception occurred on ocr_image method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))