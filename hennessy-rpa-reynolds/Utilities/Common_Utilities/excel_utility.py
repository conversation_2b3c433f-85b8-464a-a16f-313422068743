from Utilities.Common_Utilities.logger_utility import Logger
import csv
import pandas as pd
import openpyxl
import traceback

class Excel():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    # Write row
    #   INPUT:
    #       -writer
    #       -row: list of values to be entered
    def write_row(self, writer, row): 
        try:
            writer.writerow(row)
        except Exception as e:           
            raise Exception('Exception occurred on write_row method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Create CSV
    #   INPUT:
    #       -file_path
    #   OUTPUT:
    #       -writer
    def create_csv(self, file_path): 
        try:
            writer = csv.writer(file_path)
            return writer
        except Exception as e:           
            raise Exception('Exception occurred on create_csv method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
    
    #Read CSV
    #   INPUT:
    #       -file_path
    #   OUTPUT:
    #       -data
    def read_csv(self, file_path): 
        try:
            data = pd.read_csv(file_path)
            return data 
        except Exception as e:           
            raise Exception('Exception occurred on read_csv method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Read excel sheet
    #   INPUT:
    #       -file_path
    #       -sheet_name
    #   OUTPUT:
    #       -data
    def read_excel_sheet(self, file_path, sheet_name): 
        try:
            data = pd.read_excel(file_path, sheet_name)
            return data
        except FileNotFoundError:
            raise Exception('File not found')        
        except Exception as e:           
            raise Exception('Exception occurred on read_excel_sheet method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Read excel cell (Only for xlsx)
    #   INPUT:
    #       -file_path
    #       -sheet_name
    #       -cell
    #   OUTPUT:
    #       -cell_value
    def read_cell_value(self, file_path, sheet_name, cell): 
        try:
            wb = openpyxl.load_workbook(file_path)
            worksheet = wb[sheet_name]
            cell_value = worksheet[cell].value
            return cell_value
        except FileNotFoundError:
            raise Exception('File not found')        
        except Exception as e:           
            raise Exception('Exception occurred on read_cell_value method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Set cell value (Only for xlsx)
    #   INPUT:
    #       -file_path
    #       -sheet_name
    #       -cell
    #       -value
    #   OUTPUT:
    #       -cell_value
    def set_cell_value(self, file_path, sheet_name, cell, value): 
        try:
            wb = openpyxl.load_workbook(file_path)
            worksheet = wb[sheet_name]
            worksheet[cell] = value
            wb.save(file_path)
        except FileNotFoundError:
            raise Exception('File not found')        
        except Exception as e:           
            raise Exception('Exception occurred on set_cell_value method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Create xlsx
    #   INPUT:
    #       -file_path: path where file will be saved
    #       -sheeet_name: this is optional, if not specified it will be Sheet1
    def create_excel(self, file_path, sheet_name="Sheet1"): 
        try:
            wb = openpyxl.Workbook()
            sheet = wb.active
            sheet.title = sheet_name
            wb.save(file_path)   
        except Exception as e:           
            raise Exception('Exception occurred on create_excel method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Paste dataframe into excel
    #   INPUT:
    #       -dataframe
    #       -include_headers: if not specified it will be true
    #       -start_row: row where dataframe will be pasted
    #       -sheet_name
    def paste_dataframe_into_excel(self, file_path, dataframe, include_headers=True, start_row=1, sheet_name=None):
        try:
            book = openpyxl.load_workbook(file_path)

            if sheet_name:
                if sheet_name not in book.sheetnames:
                    book.create_sheet(sheet_name)
                sheet = book[sheet_name] 
            else:
                sheet = book.active

            start_row -= 1

            if include_headers:
                for col_num, column_name in enumerate(dataframe.columns, 1):
                    sheet.cell(row=start_row + 1, column=col_num).value = column_name

            for row_num, row_data in enumerate(dataframe.values, start_row + 2):
                for col_num, cell_value in enumerate(row_data, 1):
                    sheet.cell(row=row_num, column=col_num).value = cell_value

            book.save(file_path)
        except FileNotFoundError:
                raise Exception('File not found')        
        except Exception as e:           
            raise Exception('Exception occurred on paste_dataframe_into_excel method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))