import os
from Utilities.Common_Utilities.logger_utility import Logger
import sqlite3 as sl
import traceback

class Sql_db():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    # Init sql db
    #   INPUT:
    #       -db_path: path where .db is located
    #       -db_name: name of .db
    #   OUTPUT:
    #       -con: connection stablished
    def init_db(self, db_path, db_name):
        try:
            con = sl.connect(os.path.join(db_path, db_name), timeout=30.0)
            return con
        except Exception as e:           
            raise Exception ('Exception occurred on init_db method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Execute query
    #   INPUT:
    #       -sql_query: query to execute
    #       -con: connection with .db
    #   OUTPUT:
    #       -results: results of the DB in case a SELECT is executed
    def execute_query(self, sql_query, con):
        try:
            type_of_request = sql_query.split(" ")[0]
            match type_of_request:
                case "SELECT":
                    cursor = con.execute(sql_query)
                    return cursor.fetchall()
                case "INSERT" | "UPDATE" | "DELETE" | "DROP" | "TRUNCATE" | "CREATE":
                    cursor = con.execute(sql_query)
                    con.commit()
                    return
                case _:
                    raise ("System Exception: Invalid query")
        except Exception as e:
            raise Exception ('Exception occurred on execute_query method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))