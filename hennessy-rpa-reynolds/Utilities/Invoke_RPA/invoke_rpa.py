import traceback
import rpa as r
import pyautogui
import pyperclip
import time
import traceback

class Invoke_RPA():

    def __init__(self, tagui_location, timeout, visual_automation, chrome_browser, turbo_mode=True, error=True, debug=True):
        r.tagui_location(tagui_location)
        r.init(visual_automation = visual_automation, chrome_browser = chrome_browser, turbo_mode = turbo_mode)
        r.timeout(timeout)
        r.error(error)
        r.debug(debug)

    def get_timeout(self):
        try:
            timeout = r.timeout()
            return timeout
        except Exception as e:
            raise Exception('Exception occurred on get_timeout method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def set_timeout(self, timeout):
        try:
            r.timeout(float(timeout))
        except Exception as e:
            raise Exception('Exception occurred on set_timeout method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def navigate_to_url(self, url):
        try:
            r.url(url)
        except Exception as e:
            raise Exception('Exception occurred on navigate_to_url method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def download(self, url):
        try:
            r.download(url)
        except Exception as e:
            raise Exception('Exception occurred on download method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def click(self, identifier):
        try:
            r.click(identifier)
        except Exception as e:
            raise Exception('Exception occurred on click method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def double_click(self, identifier):
        try:
            r.dclick(identifier)
        except Exception as e:
            raise Exception('Exception occurred on double_click method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def double_click_coords(self, coord_x, coord_y):
        try:
            r.dclick(coord_x, coord_y)
        except Exception as e:
            raise Exception('Exception occurred on double_click_coords method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def right_click(self, identifier):
        try:
            r.rclick(identifier)
        except Exception as e:
            raise Exception('Exception occurred on right_click method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def click_coords(self, coord1, coord2):
        try:
            r.click(coord1, coord2)
        except Exception as e:
            raise Exception('Exception occurred on click_coords method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def right_click_coords(self, coord1, coord2):
        try:
            r.rclick(coord1, coord2)
        except Exception as e:
            raise Exception('Exception occurred on right_click_coords method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def return_coords(self):
        try:
            return [r.mouse_x(), r.mouse_y()]
        except Exception as e:
            raise Exception('Exception occurred on left_click_coords method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def hover(self, identifier):
        try:
            r.hover(identifier)
        except Exception as e:
            raise Exception('Exception occurred on hover method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def element_exists(self, identifier, wait_time):
        try:
            current_timeout = self.get_timeout()
            self.set_timeout(wait_time)
            exist = r.exist(identifier)
            self.set_timeout(current_timeout)
            return exist
        except Exception as e:
            raise Exception('Exception occurred on element_exists method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def send_keyboard(self, keys):
        try:
            r.keyboard(keys)
        except Exception as e:
            raise Exception('Exception occurred on send_keyboard method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def type_text(self, identifier, text):
        try:
            r.type(identifier, text)
        except Exception as e:
            raise Exception('Exception occurred on type_text method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def set_clipboard(self, text):
        try:
            r.clipboard(text)
        except Exception as e:
            raise Exception('Exception occurred on set_clipboard method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def paste_clipboard(self, text):
        try:
            self.set_clipboard(text)
            self.send_keyboard('[ctrl]v')
        except Exception as e:
            raise Exception('Exception occurred on paste_clipboard method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def get_clipboard(self):
        try:
            return r.clipboard()
        except Exception as e:
            raise Exception('Exception occurred on get_clipboard method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def dom(self, statement_to_run):
        try:
            return r.dom(statement_to_run)
        except Exception as e:
            raise Exception('Exception occurred on dom method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def attach_frame(self, frame_name):
        try:
            r.frame(frame_name)
        except Exception as e:
            raise Exception('Exception occurred on attach_frame method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))

    def close(self):
        try:
            r.close()
        except Exception as e:
            raise Exception('Exception occurred on close method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def focus(self, identifier):
        try:
            r.focus(identifier)
        except Exception as e:
            raise Exception('Exception occurred on focus method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
    
    def get_text(self):
        try:
            time.sleep(0.1)
            pyautogui.keyDown('ctrl')
            time.sleep(0.3)
            pyautogui.press('a')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')
            
            time.sleep(0.1)

            pyautogui.keyDown('ctrl')
            time.sleep(0.3)
            pyautogui.press('c')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')
            time.sleep(0.1)
            return pyperclip.paste()
        except Exception as e:
            raise Exception('Exception occurred on get_text method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        
    def get_text_without_ctrl_a(self):
        try:
            time.sleep(0.1)
            pyautogui.keyDown('ctrl')
            time.sleep(0.5)
            pyautogui.keyDown('shift')
            time.sleep(0.5)
            pyautogui.press('right')
            time.sleep(0.5)
            pyautogui.keyUp('shift')

            time.sleep(0.1)

            pyautogui.keyDown('ctrl')
            time.sleep(0.5)
            pyautogui.press('c')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')
            time.sleep(0.5)
            return pyperclip.paste()
        except Exception as e:
            raise Exception('Exception occurred on get_text method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))
        